"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectDeploymentService = exports.DeploymentStatus = void 0;
const vscode = __importStar(require("vscode"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
const ProjectConfiguration_1 = require("../models/ProjectConfiguration");
const TomcatInstanceManager_1 = require("./TomcatInstanceManager");
/**
 * 部署状态枚举
 */
var DeploymentStatus;
(function (DeploymentStatus) {
    DeploymentStatus["PENDING"] = "pending";
    DeploymentStatus["BUILDING"] = "building";
    DeploymentStatus["DEPLOYING"] = "deploying";
    DeploymentStatus["SUCCESS"] = "success";
    DeploymentStatus["FAILED"] = "failed";
})(DeploymentStatus = exports.DeploymentStatus || (exports.DeploymentStatus = {}));
/**
 * 项目部署服务
 */
class ProjectDeploymentService {
    constructor() {
        this.deploymentQueue = new Map();
        this.instanceManager = TomcatInstanceManager_1.TomcatInstanceManager.getInstance();
    }
    /**
     * 获取单例实例
     */
    static getInstance() {
        if (!ProjectDeploymentService.instance) {
            ProjectDeploymentService.instance = new ProjectDeploymentService();
        }
        return ProjectDeploymentService.instance;
    }
    /**
     * 部署项目到Tomcat实例
     */
    async deployProject(project, instanceId) {
        const deploymentKey = `${project.getId()}-${instanceId}`;
        // 检查是否已有部署任务在进行
        if (this.deploymentQueue.has(deploymentKey)) {
            return await this.deploymentQueue.get(deploymentKey);
        }
        const deploymentPromise = this.performDeployment(project, instanceId);
        this.deploymentQueue.set(deploymentKey, deploymentPromise);
        try {
            const result = await deploymentPromise;
            return result;
        }
        finally {
            this.deploymentQueue.delete(deploymentKey);
        }
    }
    /**
     * 执行部署
     */
    async performDeployment(project, instanceId) {
        try {
            const instance = this.instanceManager.getInstance(instanceId);
            if (!instance) {
                return {
                    status: DeploymentStatus.FAILED,
                    message: `Tomcat instance ${instanceId} not found`,
                    error: "Instance not found",
                };
            }
            // 1. 构建项目
            const buildResult = await this.buildProject(project);
            if (buildResult.status === DeploymentStatus.FAILED) {
                return buildResult;
            }
            // 2. 部署WAR文件
            const deployResult = await this.deployWarFile(project, instance);
            if (deployResult.status === DeploymentStatus.FAILED) {
                return deployResult;
            }
            // 3. 更新实例配置
            await this.updateInstanceDeployment(project, instance);
            // 4. 返回成功结果
            const config = instance.getConfiguration();
            const url = instance.getHttpUrl(project.getContextPath());
            return {
                status: DeploymentStatus.SUCCESS,
                message: `Project ${project.getName()} deployed successfully`,
                warPath: project.getWarFilePath(),
                deployedUrl: url,
                buildOutput: buildResult.buildOutput,
            };
        }
        catch (error) {
            return {
                status: DeploymentStatus.FAILED,
                message: `Deployment failed: ${error}`,
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }
    /**
     * 构建项目
     */
    async buildProject(project) {
        if (!project.isAutoBuildEnabled()) {
            // 检查WAR文件是否存在
            const warPath = project.getWarFilePath();
            if (!fs.existsSync(warPath)) {
                const config = project.getConfiguration();
                return {
                    status: DeploymentStatus.FAILED,
                    message: "WAR file not found and auto-build is disabled",
                    error: `WAR file not found: ${warPath}\n\nProject type: ${config.build.type}\nExpected location: ${config.projectPath}/${config.build.outputDirectory}/${config.build.warFileName}\n\nPlease build the project first or enable auto-build.`,
                };
            }
            return {
                status: DeploymentStatus.SUCCESS,
                message: "Using existing WAR file",
            };
        }
        return new Promise((resolve) => {
            const config = project.getConfiguration();
            const buildCommand = this.parseBuildCommand(config.build.buildCommand);
            const buildProcess = (0, child_process_1.spawn)(buildCommand.command, buildCommand.args, {
                cwd: config.projectPath,
                stdio: ["pipe", "pipe", "pipe"],
            });
            let buildOutput = "";
            let errorOutput = "";
            buildProcess.stdout?.on("data", (data) => {
                buildOutput += data.toString();
            });
            buildProcess.stderr?.on("data", (data) => {
                errorOutput += data.toString();
            });
            buildProcess.on("exit", (code) => {
                if (code === 0) {
                    resolve({
                        status: DeploymentStatus.SUCCESS,
                        message: "Build completed successfully",
                        buildOutput,
                    });
                }
                else {
                    resolve({
                        status: DeploymentStatus.FAILED,
                        message: `Build failed with exit code ${code}`,
                        error: errorOutput,
                        buildOutput,
                    });
                }
            });
            buildProcess.on("error", (error) => {
                resolve({
                    status: DeploymentStatus.FAILED,
                    message: `Build process error: ${error.message}`,
                    error: error.message,
                });
            });
        });
    }
    /**
     * 部署WAR文件
     */
    async deployWarFile(project, instance) {
        const warPath = project.getWarFilePath();
        if (!fs.existsSync(warPath)) {
            const config = project.getConfiguration();
            return {
                status: DeploymentStatus.FAILED,
                message: "WAR file not found",
                error: `WAR file not found: ${warPath}\n\nProject type: ${config.build.type}\nExpected location: ${config.projectPath}/${config.build.outputDirectory}/${config.build.warFileName}\n\nPlease build the project first or enable auto-build.`,
            };
        }
        const config = instance.getConfiguration();
        const contextPath = project.getContextPath();
        // 确定部署目标路径
        let deployPath;
        if (contextPath === "ROOT") {
            deployPath = path.join(config.instancePath, "webapps", "ROOT.war");
        }
        else {
            const contextName = contextPath.startsWith("/")
                ? contextPath.substring(1)
                : contextPath;
            deployPath = path.join(config.instancePath, "webapps", `${contextName}.war`);
        }
        try {
            // 如果目标目录已存在，先删除
            const deployDir = deployPath.replace(".war", "");
            if (fs.existsSync(deployDir)) {
                fs.rmSync(deployDir, { recursive: true, force: true });
            }
            // 复制WAR文件
            await this.copyFile(warPath, deployPath);
            return {
                status: DeploymentStatus.SUCCESS,
                message: "WAR file deployed successfully",
            };
        }
        catch (error) {
            return {
                status: DeploymentStatus.FAILED,
                message: `Failed to deploy WAR file: ${error}`,
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }
    /**
     * 更新实例部署信息
     */
    async updateInstanceDeployment(project, instance) {
        const app = {
            id: project.getId(),
            name: project.getName(),
            contextPath: project.getContextPath(),
            warPath: project.getWarFilePath(),
            sourcePath: project.getProjectPath(),
            status: "deployed",
            deployedAt: new Date(),
            url: instance.getHttpUrl(project.getContextPath()),
        };
        instance.addDeployedApp(app);
        project.updateLastDeployed();
        project.setTomcatInstanceId(instance.getId());
    }
    /**
     * 取消部署应用
     */
    async undeployApplication(instanceId, appId) {
        const instance = this.instanceManager.getInstance(instanceId);
        if (!instance) {
            throw new Error(`Instance ${instanceId} not found`);
        }
        const deployedApps = instance.getDeployedApps();
        const app = deployedApps.find((a) => a.id === appId);
        if (!app) {
            throw new Error(`Application ${appId} not found`);
        }
        const config = instance.getConfiguration();
        // 删除WAR文件和解压目录
        const contextPath = app.contextPath;
        let warFileName;
        let deployDir;
        if (contextPath === "ROOT") {
            warFileName = "ROOT.war";
            deployDir = "ROOT";
        }
        else {
            const contextName = contextPath.startsWith("/")
                ? contextPath.substring(1)
                : contextPath;
            warFileName = `${contextName}.war`;
            deployDir = contextName;
        }
        const warPath = path.join(config.instancePath, "webapps", warFileName);
        const deployDirPath = path.join(config.instancePath, "webapps", deployDir);
        // 删除文件和目录
        if (fs.existsSync(warPath)) {
            fs.unlinkSync(warPath);
        }
        if (fs.existsSync(deployDirPath)) {
            fs.rmSync(deployDirPath, { recursive: true, force: true });
        }
        // 从实例中移除应用
        instance.removeDeployedApp(appId);
    }
    /**
     * 检测项目类型
     */
    detectProjectType(projectPath) {
        if (fs.existsSync(path.join(projectPath, "pom.xml"))) {
            return ProjectConfiguration_1.ProjectType.MAVEN;
        }
        if (fs.existsSync(path.join(projectPath, "build.gradle")) ||
            fs.existsSync(path.join(projectPath, "build.gradle.kts"))) {
            return ProjectConfiguration_1.ProjectType.GRADLE;
        }
        return ProjectConfiguration_1.ProjectType.PLAIN_JAVA;
    }
    /**
     * 扫描工作区中的Java Web项目（只包含WAR包装类型的项目）
     */
    async scanWorkspaceForProjects() {
        const projects = [];
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return projects;
        }
        for (const folder of workspaceFolders) {
            const folderPath = folder.uri.fsPath;
            await this.scanDirectoryForProjects(folderPath, projects);
        }
        return projects;
    }
    /**
     * 递归扫描目录查找项目
     */
    async scanDirectoryForProjects(dirPath, projects, maxDepth = 3, currentDepth = 0) {
        if (currentDepth >= maxDepth) {
            return;
        }
        try {
            const entries = fs.readdirSync(dirPath, { withFileTypes: true });
            // 检查当前目录是否是Web项目
            const projectType = this.detectProjectType(dirPath);
            if (await this.isWebProject(dirPath, projectType)) {
                const projectName = path.basename(dirPath);
                projects.push({
                    path: dirPath,
                    name: projectName,
                    type: projectType,
                });
                return; // 找到项目后不再深入扫描
            }
            // 递归扫描子目录
            for (const entry of entries) {
                if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
                    const subDirPath = path.join(dirPath, entry.name);
                    await this.scanDirectoryForProjects(subDirPath, projects, maxDepth, currentDepth + 1);
                }
            }
        }
        catch (error) {
            // 忽略无法访问的目录
        }
    }
    /**
     * 检查是否为Web项目（WAR包装类型）
     */
    async isWebProject(projectPath, projectType) {
        // 对于普通Java项目，检查是否有web.xml
        if (projectType === ProjectConfiguration_1.ProjectType.PLAIN_JAVA) {
            return this.hasWebXml(projectPath);
        }
        // 对于Maven项目，检查pom.xml中的packaging
        if (projectType === ProjectConfiguration_1.ProjectType.MAVEN) {
            return await this.isMavenWarProject(projectPath);
        }
        // 对于Gradle项目，检查build.gradle中的war插件
        if (projectType === ProjectConfiguration_1.ProjectType.GRADLE) {
            return await this.isGradleWarProject(projectPath);
        }
        return false;
    }
    /**
     * 检查是否有web.xml文件
     */
    hasWebXml(projectPath) {
        const webXmlPaths = [
            path.join(projectPath, "src", "main", "webapp", "WEB-INF", "web.xml"),
            path.join(projectPath, "web", "WEB-INF", "web.xml"),
            path.join(projectPath, "WebContent", "WEB-INF", "web.xml"),
        ];
        return webXmlPaths.some((webXmlPath) => fs.existsSync(webXmlPath));
    }
    /**
     * 检查Maven项目是否为WAR包装类型
     */
    async isMavenWarProject(projectPath) {
        const pomPath = path.join(projectPath, "pom.xml");
        if (!fs.existsSync(pomPath)) {
            return false;
        }
        try {
            const pomContent = fs.readFileSync(pomPath, "utf-8");
            // 检查packaging标签
            const packagingMatch = pomContent.match(/<packaging>\s*(\w+)\s*<\/packaging>/i);
            if (packagingMatch) {
                return packagingMatch[1].toLowerCase() === "war";
            }
            // 如果没有packaging标签，检查是否有webapp目录结构或web.xml
            return (this.hasWebXml(projectPath) || this.hasWebappDirectory(projectPath));
        }
        catch (error) {
            console.error("Error reading pom.xml:", error);
            return false;
        }
    }
    /**
     * 检查Gradle项目是否为WAR项目
     */
    async isGradleWarProject(projectPath) {
        const buildGradlePaths = [
            path.join(projectPath, "build.gradle"),
            path.join(projectPath, "build.gradle.kts"),
        ];
        for (const buildPath of buildGradlePaths) {
            if (fs.existsSync(buildPath)) {
                try {
                    const buildContent = fs.readFileSync(buildPath, "utf-8");
                    // 检查是否应用了war插件
                    if (buildContent.includes("apply plugin: 'war'") ||
                        buildContent.includes('apply plugin: "war"') ||
                        buildContent.includes("id 'war'") ||
                        buildContent.includes('id "war"') ||
                        (buildContent.includes("plugins {") && buildContent.includes("war"))) {
                        return true;
                    }
                    // 检查是否有webapp目录结构
                    if (this.hasWebappDirectory(projectPath)) {
                        return true;
                    }
                }
                catch (error) {
                    console.error("Error reading build.gradle:", error);
                }
            }
        }
        return false;
    }
    /**
     * 检查是否有webapp目录结构
     */
    hasWebappDirectory(projectPath) {
        const webappPaths = [
            path.join(projectPath, "src", "main", "webapp"),
            path.join(projectPath, "web"),
            path.join(projectPath, "WebContent"),
        ];
        return webappPaths.some((webappPath) => fs.existsSync(webappPath));
    }
    /**
     * 检查是否应该跳过目录
     */
    shouldSkipDirectory(dirName) {
        const skipDirs = [
            "node_modules",
            ".git",
            ".svn",
            ".hg",
            "target",
            "build",
            "out",
            "dist",
            ".vscode",
            ".idea",
            ".eclipse",
        ];
        return skipDirs.includes(dirName) || dirName.startsWith(".");
    }
    /**
     * 解析构建命令
     */
    parseBuildCommand(command) {
        const parts = command.trim().split(/\s+/);
        return {
            command: parts[0],
            args: parts.slice(1),
        };
    }
    /**
     * 复制文件
     */
    async copyFile(src, dest) {
        return new Promise((resolve, reject) => {
            const readStream = fs.createReadStream(src);
            const writeStream = fs.createWriteStream(dest);
            readStream.on("error", reject);
            writeStream.on("error", reject);
            writeStream.on("finish", resolve);
            readStream.pipe(writeStream);
        });
    }
}
exports.ProjectDeploymentService = ProjectDeploymentService;
//# sourceMappingURL=ProjectDeploymentService.js.map