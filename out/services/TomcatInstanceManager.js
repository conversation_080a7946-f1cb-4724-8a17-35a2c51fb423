"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TomcatInstanceManager = void 0;
const vscode = __importStar(require("vscode"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
const TomcatInstance_1 = require("../models/TomcatInstance");
const PortManager_1 = require("../models/PortManager");
const ConfigurationManager_1 = require("./ConfigurationManager");
/**
 * Tomcat实例管理器
 */
class TomcatInstanceManager {
    constructor() {
        this.instances = new Map();
        this.processes = new Map();
        this.outputChannels = new Map();
        this.portManager = PortManager_1.PortManager.getInstance();
        this.configManager = ConfigurationManager_1.ConfigurationManager.getInstance();
        this.loadInstances();
    }
    /**
     * 获取单例实例
     */
    static getInstance() {
        if (!TomcatInstanceManager.instance) {
            TomcatInstanceManager.instance = new TomcatInstanceManager();
        }
        return TomcatInstanceManager.instance;
    }
    /**
     * 创建新的Tomcat实例
     */
    async createInstance(config) {
        // 生成唯一ID
        const id = this.generateInstanceId();
        // 生成可用的端口配置
        const ports = await this.portManager.generateAvailablePortConfiguration();
        if (!ports) {
            throw new Error("Unable to find available ports for new instance");
        }
        // 创建实例配置
        const instanceConfig = {
            id,
            name: config.name || `Tomcat-${id}`,
            description: config.description || "",
            baseTomcatPath: config.baseTomcatPath || "",
            instancePath: path.join(this.getInstancesDirectory(), id),
            ports,
            jvm: config.jvm || {
                jrePath: this.getDefaultJrePath(),
                minHeapSize: "256m",
                maxHeapSize: "512m",
                additionalArgs: [],
            },
            browser: config.browser || {
                type: TomcatInstance_1.BrowserType.DEFAULT,
                autoOpen: true,
                defaultPage: "",
            },
            deployedApps: [],
            status: TomcatInstance_1.TomcatInstanceStatus.STOPPED,
            createdAt: new Date(),
            lastModified: new Date(),
            hotDeployEnabled: true,
            logPath: path.join(this.getInstancesDirectory(), id, "logs"),
        };
        // 验证配置
        const instance = new TomcatInstance_1.TomcatInstance(instanceConfig);
        const errors = instance.validateConfiguration();
        if (errors.length > 0) {
            throw new Error(`Invalid configuration: ${errors.join(", ")}`);
        }
        // 预留端口
        const portsReserved = await this.portManager.reserveInstancePorts(ports, id);
        if (!portsReserved) {
            throw new Error("Failed to reserve ports for instance");
        }
        try {
            // 创建实例目录结构
            await this.createInstanceDirectory(instance);
            // 保存实例
            this.instances.set(id, instance);
            await this.saveInstances();
            return instance;
        }
        catch (error) {
            // 如果创建失败，释放端口
            this.portManager.releaseInstancePorts(id);
            throw error;
        }
    }
    /**
     * 删除Tomcat实例
     */
    async deleteInstance(instanceId) {
        const instance = this.instances.get(instanceId);
        if (!instance) {
            throw new Error(`Instance ${instanceId} not found`);
        }
        // 如果实例正在运行，先停止它
        if (instance.getStatus() === TomcatInstance_1.TomcatInstanceStatus.RUNNING) {
            await this.stopInstance(instanceId);
        }
        // 释放端口
        this.portManager.releaseInstancePorts(instanceId);
        // 删除实例目录
        const instancePath = instance.getConfiguration().instancePath;
        if (fs.existsSync(instancePath)) {
            await this.deleteDirectory(instancePath);
        }
        // 清理输出通道
        this.disposeOutputChannel(instanceId);
        // 从内存中移除
        this.instances.delete(instanceId);
        await this.saveInstances();
    }
    /**
     * 启动Tomcat实例
     */
    async startInstance(instanceId) {
        const instance = this.instances.get(instanceId);
        if (!instance) {
            throw new Error(`Instance ${instanceId} not found`);
        }
        if (instance.getStatus() === TomcatInstance_1.TomcatInstanceStatus.RUNNING) {
            throw new Error(`Instance ${instanceId} is already running`);
        }
        instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.STARTING);
        try {
            const config = instance.getConfiguration();
            const startupScript = this.getStartupScript(config);
            const env = this.buildEnvironment(config);
            const childProcess = (0, child_process_1.spawn)(startupScript.command, startupScript.args, {
                cwd: config.instancePath,
                env: { ...process.env, ...env },
                stdio: ["pipe", "pipe", "pipe"],
            });
            this.processes.set(instanceId, childProcess);
            // 创建日志收集
            let startupLog = "";
            let errorLog = "";
            // 获取输出通道
            const outputChannel = this.getOutputChannel(instanceId);
            outputChannel.clear();
            outputChannel.show(true); // 显示输出通道但不获取焦点
            // 输出启动信息
            const instanceName = instance.getName();
            outputChannel.appendLine(`=== Tomcat实例 "${instanceName}" 启动日志 ===`);
            outputChannel.appendLine(`实例ID: ${instanceId}`);
            outputChannel.appendLine(`HTTP端口: ${config.ports.httpPort}`);
            outputChannel.appendLine(`实例路径: ${config.instancePath}`);
            outputChannel.appendLine(`JRE路径: ${config.jvm.jrePath}`);
            outputChannel.appendLine(`JAVA_HOME: ${env.JAVA_HOME}`);
            outputChannel.appendLine(`启动时间: ${new Date().toLocaleString()}`);
            outputChannel.appendLine(`${"=".repeat(50)}`);
            outputChannel.appendLine("");
            // 监听stdout
            childProcess.stdout?.on("data", (data) => {
                const output = data.toString();
                startupLog += output;
                // 输出到控制台（调试用）
                console.log(`[${instanceId}] STDOUT:`, output);
                // 输出到VSCode输出通道
                outputChannel.append(output);
                // 将日志写入文件
                this.writeToLogFile(config.logPath, "catalina.out", output);
                // 检查启动成功的关键信息
                if (output.includes("Server startup in") ||
                    output.includes("Tomcat started")) {
                    console.log(`[${instanceId}] 检测到Tomcat启动成功信号`);
                    outputChannel.appendLine(`\n✅ Tomcat启动成功！`);
                }
                // 检查启动错误信息
                if (output.includes("SEVERE") ||
                    output.includes("ERROR") ||
                    output.includes("Exception")) {
                    console.error(`[${instanceId}] 检测到启动错误:`, output);
                    outputChannel.appendLine(`\n❌ 检测到启动错误`);
                }
            });
            // 监听stderr
            childProcess.stderr?.on("data", (data) => {
                const output = data.toString();
                errorLog += output;
                // 输出到控制台（调试用）
                console.error(`[${instanceId}] STDERR:`, output);
                // 输出到VSCode输出通道
                outputChannel.append(output);
                // 将错误日志写入文件
                this.writeToLogFile(config.logPath, "catalina.out", output);
                // 检查严重错误
                if (output.includes("java.lang.OutOfMemoryError") ||
                    output.includes("java.net.BindException") ||
                    output.includes("Address already in use")) {
                    console.error(`[${instanceId}] 检测到严重启动错误:`, output);
                    outputChannel.appendLine(`\n🚨 检测到严重启动错误！`);
                }
            });
            // 监听进程事件
            childProcess.on("exit", (code) => {
                this.processes.delete(instanceId);
                if (code === 0) {
                    outputChannel.appendLine(`\n✅ Tomcat进程正常退出`);
                    instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.STOPPED);
                }
                else {
                    console.error(`Instance ${instanceId} exited with code ${code}`);
                    console.error(`Startup log:`, startupLog);
                    console.error(`Error log:`, errorLog);
                    outputChannel.appendLine(`\n❌ Tomcat进程异常退出，退出代码: ${code}`);
                    instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.ERROR);
                }
                outputChannel.appendLine(`退出时间: ${new Date().toLocaleString()}`);
            });
            childProcess.on("error", (error) => {
                console.error(`Instance ${instanceId} process error:`, error);
                console.error(`Startup log:`, startupLog);
                console.error(`Error log:`, errorLog);
                outputChannel.appendLine(`\n🚨 Tomcat进程错误: ${error.message}`);
                instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.ERROR);
                this.processes.delete(instanceId);
            });
            // 等待启动完成
            try {
                await this.waitForStartup(instance);
                instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.RUNNING);
                outputChannel.appendLine(`\n🎉 Tomcat实例启动完成！`);
                outputChannel.appendLine(`访问地址: http://localhost:${config.ports.httpPort}`);
                outputChannel.appendLine(`完成时间: ${new Date().toLocaleString()}`);
                // 如果配置了自动打开浏览器，则打开
                if (config.browser.autoOpen) {
                    await this.openInBrowser(instanceId);
                    outputChannel.appendLine(`🌐 已自动打开浏览器`);
                }
            }
            catch (startupError) {
                console.error(`Instance ${instanceId} startup failed:`, startupError);
                outputChannel.appendLine(`\n💥 Tomcat启动失败: ${startupError instanceof Error
                    ? startupError.message
                    : String(startupError)}`);
                // 收集启动失败的详细信息
                const diagnosticInfo = await this.collectStartupDiagnostics(instance, startupLog, errorLog);
                console.error(`Diagnostic info:`, diagnosticInfo);
                // 在输出通道显示诊断信息
                outputChannel.appendLine(`\n🔍 诊断信息:`);
                outputChannel.appendLine(`- 基础Tomcat路径: ${diagnosticInfo.baseTomcatPath}`);
                outputChannel.appendLine(`- 实例路径: ${diagnosticInfo.instancePath}`);
                outputChannel.appendLine(`- JRE路径: ${diagnosticInfo.jrePath}`);
                outputChannel.appendLine(`- HTTP端口: ${diagnosticInfo.ports.httpPort}`);
                // 显示详细的错误信息给用户
                const errorMessage = this.formatStartupError(startupError, diagnosticInfo);
                vscode.window.showErrorMessage(errorMessage);
                instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.ERROR);
                throw startupError;
            }
        }
        catch (error) {
            instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.ERROR);
            throw error;
        }
    }
    /**
     * 停止Tomcat实例
     */
    async stopInstance(instanceId) {
        const instance = this.instances.get(instanceId);
        if (!instance) {
            throw new Error(`Instance ${instanceId} not found`);
        }
        if (instance.getStatus() !== TomcatInstance_1.TomcatInstanceStatus.RUNNING) {
            throw new Error(`Instance ${instanceId} is not running`);
        }
        instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.STOPPING);
        // 获取输出通道并记录停止信息
        const outputChannel = this.getOutputChannel(instanceId);
        outputChannel.appendLine(`\n🛑 正在停止Tomcat实例...`);
        outputChannel.appendLine(`停止时间: ${new Date().toLocaleString()}`);
        const process = this.processes.get(instanceId);
        if (process) {
            // 尝试优雅关闭
            const config = instance.getConfiguration();
            const shutdownScript = this.getShutdownScript(config);
            try {
                outputChannel.appendLine(`🔄 尝试优雅关闭Tomcat...`);
                const shutdownProcess = (0, child_process_1.spawn)(shutdownScript.command, shutdownScript.args, {
                    cwd: config.instancePath,
                    env: this.buildEnvironment(config),
                });
                // 等待关闭完成
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        // 如果优雅关闭超时，强制杀死进程
                        outputChannel.appendLine(`⚠️ 优雅关闭超时，强制终止进程`);
                        process.kill("SIGKILL");
                        reject(new Error("Shutdown timeout, process killed"));
                    }, 10000);
                    shutdownProcess.on("exit", () => {
                        clearTimeout(timeout);
                        outputChannel.appendLine(`✅ Tomcat优雅关闭完成`);
                        resolve();
                    });
                    shutdownProcess.on("error", (error) => {
                        clearTimeout(timeout);
                        outputChannel.appendLine(`❌ 关闭过程出错: ${error.message}`);
                        reject(error);
                    });
                });
            }
            catch (error) {
                // 如果优雅关闭失败，强制杀死进程
                outputChannel.appendLine(`⚠️ 优雅关闭失败，强制终止进程`);
                process.kill("SIGKILL");
            }
        }
        instance.setStatus(TomcatInstance_1.TomcatInstanceStatus.STOPPED);
        outputChannel.appendLine(`🏁 Tomcat实例已停止`);
    }
    /**
     * 重启Tomcat实例
     */
    async restartInstance(instanceId) {
        await this.stopInstance(instanceId);
        await new Promise((resolve) => setTimeout(resolve, 2000)); // 等待2秒
        await this.startInstance(instanceId);
    }
    /**
     * 获取所有实例
     */
    getAllInstances() {
        return Array.from(this.instances.values());
    }
    /**
     * 获取指定实例
     */
    getInstance(instanceId) {
        return this.instances.get(instanceId);
    }
    /**
     * 更新实例配置
     */
    async updateInstance(instanceId, updates) {
        const instance = this.instances.get(instanceId);
        if (!instance) {
            throw new Error(`Instance ${instanceId} not found`);
        }
        // 如果更新了端口配置，需要验证和重新预留端口
        if (updates.ports) {
            const errors = await this.portManager.validatePortConfiguration(updates.ports, instanceId);
            if (errors.length > 0) {
                throw new Error(`Port validation failed: ${errors.join(", ")}`);
            }
            // 释放旧端口，预留新端口
            this.portManager.releaseInstancePorts(instanceId);
            const portsReserved = await this.portManager.reserveInstancePorts(updates.ports, instanceId);
            if (!portsReserved) {
                throw new Error("Failed to reserve new ports");
            }
        }
        instance.updateConfiguration(updates);
        await this.saveInstances();
        // 如果实例正在运行且更新了关键配置，需要重启
        if (instance.getStatus() === TomcatInstance_1.TomcatInstanceStatus.RUNNING &&
            this.requiresRestart(updates)) {
            await this.restartInstance(instanceId);
        }
    }
    /**
     * 在浏览器中打开实例
     */
    async openInBrowser(instanceId, contextPath = "") {
        const instance = this.instances.get(instanceId);
        if (!instance) {
            throw new Error(`Instance ${instanceId} not found`);
        }
        const config = instance.getConfiguration();
        const url = instance.getHttpUrl(contextPath);
        await this.openUrl(url, config.browser.type, config.browser.customPath);
    }
    /**
     * 生成实例ID
     */
    generateInstanceId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    /**
     * 获取实例目录
     */
    getInstancesDirectory() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return path.join(workspaceFolder.uri.fsPath, ".vscode", "tomcat-instances");
        }
        return path.join(require("os").homedir(), ".vscode-tomcat-instances");
    }
    /**
     * 获取默认JRE路径
     */
    getDefaultJrePath() {
        const config = vscode.workspace.getConfiguration("tomcatManager");
        return (config.get("defaultJrePath") || process.env.JAVA_HOME || "java");
    }
    /**
     * 创建实例目录结构
     */
    async createInstanceDirectory(instance) {
        const config = instance.getConfiguration();
        const instancePath = config.instancePath;
        // 创建基础目录
        await this.ensureDirectory(instancePath);
        await this.ensureDirectory(path.join(instancePath, "conf"));
        await this.ensureDirectory(path.join(instancePath, "logs"));
        await this.ensureDirectory(path.join(instancePath, "temp"));
        await this.ensureDirectory(path.join(instancePath, "work"));
        await this.ensureDirectory(path.join(instancePath, "webapps"));
        // 复制配置文件
        await this.copyTomcatConfiguration(config);
    }
    /**
     * 复制Tomcat配置文件
     */
    async copyTomcatConfiguration(config) {
        const baseTomcatConf = path.join(config.baseTomcatPath, "conf");
        const instanceConf = path.join(config.instancePath, "conf");
        // 复制基础配置文件
        const configFiles = [
            "server.xml",
            "web.xml",
            "context.xml",
            "tomcat-users.xml",
        ];
        for (const file of configFiles) {
            const srcFile = path.join(baseTomcatConf, file);
            const destFile = path.join(instanceConf, file);
            if (fs.existsSync(srcFile)) {
                await this.copyFile(srcFile, destFile);
            }
        }
        // 修改server.xml中的端口配置
        await this.updateServerXml(config);
    }
    /**
     * 更新server.xml文件
     */
    async updateServerXml(config) {
        const serverXmlPath = path.join(config.instancePath, "conf", "server.xml");
        if (!fs.existsSync(serverXmlPath)) {
            return;
        }
        let content = fs.readFileSync(serverXmlPath, "utf8");
        // 更新端口配置
        content = content.replace(/port="8005"/, `port="${config.ports.shutdownPort}"`);
        content = content.replace(/port="8080"/, `port="${config.ports.httpPort}"`);
        content = content.replace(/port="8443"/, `port="${config.ports.httpsPort}"`);
        content = content.replace(/port="8009"/, `port="${config.ports.ajpPort}"`);
        fs.writeFileSync(serverXmlPath, content);
    }
    /**
     * 获取启动脚本
     */
    getStartupScript(config) {
        const isWindows = process.platform === "win32";
        const scriptName = isWindows ? "catalina.bat" : "catalina.sh";
        const scriptPath = path.join(config.baseTomcatPath, "bin", scriptName);
        return {
            command: isWindows ? scriptPath : "bash",
            args: isWindows ? ["run"] : [scriptPath, "run"],
        };
    }
    /**
     * 获取关闭脚本
     */
    getShutdownScript(config) {
        const isWindows = process.platform === "win32";
        const scriptName = isWindows ? "catalina.bat" : "catalina.sh";
        const scriptPath = path.join(config.baseTomcatPath, "bin", scriptName);
        return {
            command: isWindows ? scriptPath : "bash",
            args: isWindows ? ["stop"] : [scriptPath, "stop"],
        };
    }
    /**
     * 构建环境变量
     */
    buildEnvironment(config) {
        return {
            JAVA_HOME: this.getJavaHome(config.jvm.jrePath),
            CATALINA_HOME: config.baseTomcatPath,
            CATALINA_BASE: config.instancePath,
            CATALINA_OPTS: [
                `-Xms${config.jvm.minHeapSize}`,
                `-Xmx${config.jvm.maxHeapSize}`,
                ...config.jvm.additionalArgs,
            ].join(" "),
        };
    }
    /**
     * 根据JRE路径获取正确的JAVA_HOME
     */
    getJavaHome(jrePath) {
        // jrePath 通常指向 bin/java 或 java 可执行文件
        let javaHome = path.dirname(jrePath);
        // 如果路径以 /bin 结尾，则获取其父目录
        if (path.basename(javaHome) === "bin") {
            javaHome = path.dirname(javaHome);
        }
        // 对于macOS的JDK，检查是否需要添加 /Home
        if (process.platform === "darwin") {
            // 如果路径包含 JavaVirtualMachines 且以 Contents 结尾
            if (javaHome.includes("JavaVirtualMachines") &&
                javaHome.endsWith("Contents")) {
                const homeDir = path.join(javaHome, "Home");
                if (fs.existsSync(homeDir)) {
                    return homeDir;
                }
            }
        }
        return javaHome;
    }
    /**
     * 写入日志文件
     */
    writeToLogFile(logPath, fileName, content) {
        try {
            // 确保日志目录存在
            if (!fs.existsSync(logPath)) {
                fs.mkdirSync(logPath, { recursive: true });
            }
            const logFilePath = path.join(logPath, fileName);
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${content}`;
            // 追加写入日志文件
            fs.appendFileSync(logFilePath, logEntry);
        }
        catch (error) {
            console.error("Failed to write to log file:", error);
        }
    }
    /**
     * 获取或创建输出通道
     */
    getOutputChannel(instanceId) {
        if (!this.outputChannels.has(instanceId)) {
            const instance = this.instances.get(instanceId);
            const instanceName = instance ? instance.getName() : instanceId;
            const channelName = `Tomcat - ${instanceName}`;
            const outputChannel = vscode.window.createOutputChannel(channelName);
            this.outputChannels.set(instanceId, outputChannel);
        }
        return this.outputChannels.get(instanceId);
    }
    /**
     * 清理输出通道
     */
    disposeOutputChannel(instanceId) {
        const outputChannel = this.outputChannels.get(instanceId);
        if (outputChannel) {
            outputChannel.dispose();
            this.outputChannels.delete(instanceId);
        }
    }
    /**
     * 显示启动日志
     */
    async showStartupLogs(instanceId) {
        const instance = this.instances.get(instanceId);
        if (!instance) {
            vscode.window.showErrorMessage("实例不存在");
            return;
        }
        const config = instance.getConfiguration();
        const logFilePath = path.join(config.logPath, "catalina.out");
        if (!fs.existsSync(logFilePath)) {
            vscode.window.showWarningMessage("日志文件不存在，可能Tomcat还未启动");
            return;
        }
        try {
            // 读取最后1000行日志
            const logContent = fs.readFileSync(logFilePath, "utf8");
            const lines = logContent.split("\n");
            const recentLines = lines.slice(-1000).join("\n");
            // 在新的编辑器窗口中显示日志
            const doc = await vscode.workspace.openTextDocument({
                content: recentLines,
                language: "log",
            });
            await vscode.window.showTextDocument(doc, {
                preview: false,
                viewColumn: vscode.ViewColumn.Beside,
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`读取日志文件失败: ${error}`);
        }
    }
    /**
     * 收集启动诊断信息
     */
    async collectStartupDiagnostics(instance, startupLog, errorLog) {
        const config = instance.getConfiguration();
        const diagnostics = {
            instanceId: instance.getId(),
            instanceName: instance.getName(),
            baseTomcatPath: config.baseTomcatPath,
            instancePath: config.instancePath,
            jrePath: config.jvm.jrePath,
            ports: config.ports,
            startupLog: startupLog.substring(0, 1000),
            errorLog: errorLog.substring(0, 1000),
            checks: {},
        };
        // 检查基础Tomcat路径
        diagnostics.checks.baseTomcatExists = fs.existsSync(config.baseTomcatPath);
        // 检查启动脚本
        const isWindows = process.platform === "win32";
        const scriptName = isWindows ? "catalina.bat" : "catalina.sh";
        const scriptPath = path.join(config.baseTomcatPath, "bin", scriptName);
        diagnostics.checks.startupScriptExists = fs.existsSync(scriptPath);
        // 检查JRE路径
        diagnostics.checks.jreExists = fs.existsSync(config.jvm.jrePath);
        // 检查实例目录
        diagnostics.checks.instancePathExists = fs.existsSync(config.instancePath);
        // 检查端口占用
        try {
            const httpPortAvailable = await this.portManager.isPortAvailable(config.ports.httpPort);
            diagnostics.checks.httpPortAvailable = httpPortAvailable;
        }
        catch (error) {
            diagnostics.checks.httpPortError = String(error);
        }
        // 检查配置文件
        const serverXmlPath = path.join(config.instancePath, "conf", "server.xml");
        diagnostics.checks.serverXmlExists = fs.existsSync(serverXmlPath);
        return diagnostics;
    }
    /**
     * 格式化启动错误信息
     */
    formatStartupError(error, diagnostics) {
        let message = `Tomcat启动失败: ${error.message}\n\n`;
        message += `实例信息:\n`;
        message += `- 名称: ${diagnostics.instanceName}\n`;
        message += `- ID: ${diagnostics.instanceId}\n`;
        message += `- HTTP端口: ${diagnostics.ports.httpPort}\n\n`;
        message += `诊断结果:\n`;
        let hasIssues = false;
        if (!diagnostics.checks.baseTomcatExists) {
            message += `❌ 基础Tomcat路径不存在: ${diagnostics.baseTomcatPath}\n`;
            hasIssues = true;
        }
        if (!diagnostics.checks.startupScriptExists) {
            message += `❌ 启动脚本不存在\n`;
            hasIssues = true;
        }
        if (!diagnostics.checks.jreExists) {
            message += `❌ JRE路径不存在: ${diagnostics.jrePath}\n`;
            hasIssues = true;
        }
        if (!diagnostics.checks.instancePathExists) {
            message += `❌ 实例目录不存在: ${diagnostics.instancePath}\n`;
            hasIssues = true;
        }
        if (!diagnostics.checks.serverXmlExists) {
            message += `❌ server.xml配置文件不存在\n`;
            hasIssues = true;
        }
        if (!diagnostics.checks.httpPortAvailable) {
            message += `❌ HTTP端口 ${diagnostics.ports.httpPort} 被占用\n`;
            hasIssues = true;
        }
        if (!hasIssues) {
            message += `✅ 所有基础检查都通过了\n\n`;
            if (error.message.includes("timeout")) {
                message += `🔍 启动超时分析:\n`;
                message += `- Tomcat可能正在启动但需要更长时间\n`;
                message += `- 可能存在应用程序初始化问题\n`;
                message += `- 建议检查catalina.out日志文件\n\n`;
                message += `💡 解决建议:\n`;
                message += `1. 右键实例选择"显示日志"查看详细启动信息\n`;
                message += `2. 检查部署的应用是否有初始化问题\n`;
                message += `3. 确认JVM内存设置是否合适\n`;
                message += `4. 检查是否有数据库连接或其他外部依赖问题\n`;
            }
        }
        if (diagnostics.errorLog) {
            message += `\n📋 最近错误日志:\n${diagnostics.errorLog}`;
        }
        if (diagnostics.startupLog) {
            message += `\n📋 最近启动日志:\n${diagnostics.startupLog}`;
        }
        message += `\n\n🔧 进一步调试:\n`;
        message += `- 右键Tomcat实例选择"显示日志"查看完整日志\n`;
        message += `- 检查实例目录: ${diagnostics.instancePath}\n`;
        message += `- 检查日志目录: ${diagnostics.instancePath}/logs/\n`;
        return message;
    }
    /**
     * 等待启动完成
     */
    async waitForStartup(instance) {
        const config = instance.getConfiguration();
        const maxAttempts = 120; // 增加到120秒（2分钟）
        const delay = 1000;
        console.log(`[${instance.getId()}] 开始等待Tomcat启动，最大等待时间: ${maxAttempts}秒`);
        for (let i = 0; i < maxAttempts; i++) {
            try {
                // 检查端口是否被占用
                const isPortAvailable = await this.portManager.isPortAvailable(config.ports.httpPort);
                if (!isPortAvailable) {
                    // 端口被占用，进一步检查Tomcat是否真正启动
                    const isHealthy = await this.checkTomcatHealth(config.ports.httpPort);
                    if (isHealthy) {
                        console.log(`[${instance.getId()}] Tomcat启动成功，耗时: ${i + 1}秒`);
                        return;
                    }
                    else {
                        console.log(`[${instance.getId()}] 端口${config.ports.httpPort}被占用但Tomcat未响应，继续等待...`);
                    }
                }
                else {
                    if (i % 10 === 0) {
                        // 每10秒打印一次进度
                        console.log(`[${instance.getId()}] 等待Tomcat启动中... (${i + 1}/${maxAttempts})`);
                    }
                }
            }
            catch (error) {
                console.error(`[${instance.getId()}] 启动检查出错:`, error);
                // 忽略错误，继续等待
            }
            await new Promise((resolve) => setTimeout(resolve, delay));
        }
        throw new Error(`Tomcat startup timeout after ${maxAttempts} seconds`);
    }
    /**
     * 检查Tomcat健康状态
     */
    async checkTomcatHealth(port) {
        try {
            const http = require("http");
            return new Promise((resolve) => {
                const req = http.request({
                    hostname: "localhost",
                    port: port,
                    path: "/",
                    method: "GET",
                    timeout: 3000,
                }, (res) => {
                    // 任何HTTP响应都表示Tomcat正在运行
                    resolve(true);
                });
                req.on("error", () => {
                    resolve(false);
                });
                req.on("timeout", () => {
                    req.destroy();
                    resolve(false);
                });
                req.end();
            });
        }
        catch (error) {
            return false;
        }
    }
    /**
     * 在浏览器中打开URL
     */
    async openUrl(url, browserType, customPath) {
        let command;
        let args = [url];
        switch (browserType) {
            case TomcatInstance_1.BrowserType.CHROME:
                command = this.getChromePath();
                break;
            case TomcatInstance_1.BrowserType.FIREFOX:
                command = this.getFirefoxPath();
                break;
            case TomcatInstance_1.BrowserType.SAFARI:
                command = "open";
                args = ["-a", "Safari", url];
                break;
            case TomcatInstance_1.BrowserType.EDGE:
                command = this.getEdgePath();
                break;
            case TomcatInstance_1.BrowserType.CUSTOM:
                if (!customPath) {
                    throw new Error("Custom browser path not specified");
                }
                command = customPath;
                break;
            default:
                // 使用系统默认浏览器
                if (process.platform === "darwin") {
                    command = "open";
                }
                else if (process.platform === "win32") {
                    command = "start";
                    args = ["", url];
                }
                else {
                    command = "xdg-open";
                }
                break;
        }
        (0, child_process_1.spawn)(command, args, { detached: true, stdio: "ignore" });
    }
    /**
     * 获取Chrome浏览器路径
     */
    getChromePath() {
        if (process.platform === "darwin") {
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome";
        }
        else if (process.platform === "win32") {
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe";
        }
        else {
            return "google-chrome";
        }
    }
    /**
     * 获取Firefox浏览器路径
     */
    getFirefoxPath() {
        if (process.platform === "darwin") {
            return "/Applications/Firefox.app/Contents/MacOS/firefox";
        }
        else if (process.platform === "win32") {
            return "C:\\Program Files\\Mozilla Firefox\\firefox.exe";
        }
        else {
            return "firefox";
        }
    }
    /**
     * 获取Edge浏览器路径
     */
    getEdgePath() {
        if (process.platform === "darwin") {
            return "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge";
        }
        else if (process.platform === "win32") {
            return "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe";
        }
        else {
            return "microsoft-edge";
        }
    }
    /**
     * 检查更新是否需要重启
     */
    requiresRestart(updates) {
        return !!(updates.ports || updates.jvm);
    }
    /**
     * 加载实例
     */
    async loadInstances() {
        try {
            const instances = await this.configManager.loadInstances();
            this.instances.clear();
            for (const config of instances) {
                const instance = TomcatInstance_1.TomcatInstance.fromJSON(config);
                this.instances.set(instance.getId(), instance);
                // 预留端口
                await this.portManager.reserveInstancePorts(config.ports, config.id);
            }
        }
        catch (error) {
            console.error("Failed to load instances:", error);
        }
    }
    /**
     * 保存实例
     */
    async saveInstances() {
        try {
            const instances = Array.from(this.instances.values()).map((instance) => instance.toJSON());
            await this.configManager.saveInstances(instances);
        }
        catch (error) {
            console.error("Failed to save instances:", error);
        }
    }
    /**
     * 确保目录存在
     */
    async ensureDirectory(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
    /**
     * 复制文件
     */
    async copyFile(src, dest) {
        return new Promise((resolve, reject) => {
            const readStream = fs.createReadStream(src);
            const writeStream = fs.createWriteStream(dest);
            readStream.on("error", reject);
            writeStream.on("error", reject);
            writeStream.on("finish", resolve);
            readStream.pipe(writeStream);
        });
    }
    /**
     * 删除目录
     */
    async deleteDirectory(dirPath) {
        if (fs.existsSync(dirPath)) {
            fs.rmSync(dirPath, { recursive: true, force: true });
        }
    }
}
exports.TomcatInstanceManager = TomcatInstanceManager;
//# sourceMappingURL=TomcatInstanceManager.js.map