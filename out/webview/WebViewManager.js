"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebViewManager = exports.WebViewType = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
/**
 * WebView面板类型枚举
 */
var WebViewType;
(function (WebViewType) {
    WebViewType["INSTANCE_CONFIG"] = "instanceConfig";
    WebViewType["PROJECT_DEPLOY"] = "projectDeploy";
    WebViewType["INSTANCE_WIZARD"] = "instanceWizard";
    WebViewType["SETTINGS_PANEL"] = "settingsPanel";
})(WebViewType = exports.WebViewType || (exports.WebViewType = {}));
/**
 * WebView管理器
 */
class WebViewManager {
    constructor(context) {
        this.panels = new Map();
        this.context = context;
    }
    /**
     * 获取单例实例
     */
    static getInstance(context) {
        if (!WebViewManager.instance && context) {
            WebViewManager.instance = new WebViewManager(context);
        }
        return WebViewManager.instance;
    }
    /**
     * 创建或显示WebView面板
     */
    createOrShowPanel(type, title, data) {
        const panelKey = `${type}_${data?.id || "default"}`;
        // 如果面板已存在，直接显示
        if (this.panels.has(panelKey)) {
            const panel = this.panels.get(panelKey);
            panel.reveal();
            return panel;
        }
        // 创建新面板
        const panel = vscode.window.createWebviewPanel(type, title, vscode.ViewColumn.One, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.file(path.join(this.context.extensionPath, "media")),
            ],
        });
        // 设置HTML内容
        panel.webview.html = this.getWebviewContent(type, panel.webview, data);
        // 处理消息
        panel.webview.onDidReceiveMessage((message) => this.handleMessage(type, message, panel), undefined, this.context.subscriptions);
        // 面板关闭时清理
        panel.onDidDispose(() => this.panels.delete(panelKey), null, this.context.subscriptions);
        this.panels.set(panelKey, panel);
        return panel;
    }
    /**
     * 获取WebView HTML内容
     */
    getWebviewContent(type, webview, data) {
        const mediaPath = vscode.Uri.file(path.join(this.context.extensionPath, "media"));
        const mediaUri = webview.asWebviewUri(mediaPath);
        switch (type) {
            case WebViewType.INSTANCE_WIZARD:
                return this.getInstanceWizardHtml(webview, mediaUri, data);
            case WebViewType.INSTANCE_CONFIG:
                return this.getInstanceConfigHtml(webview, mediaUri, data);
            case WebViewType.PROJECT_DEPLOY:
                return this.getProjectDeployHtml(webview, mediaUri, data);
            case WebViewType.SETTINGS_PANEL:
                return this.getSettingsPanelHtml(webview, mediaUri, data);
            default:
                return "<html><body><h1>Unknown panel type</h1></body></html>";
        }
    }
    /**
     * 处理WebView消息
     */
    async handleMessage(type, message, panel) {
        switch (type) {
            case WebViewType.INSTANCE_WIZARD:
                await this.handleInstanceWizardMessage(message, panel);
                break;
            case WebViewType.INSTANCE_CONFIG:
                await this.handleInstanceConfigMessage(message, panel);
                break;
            case WebViewType.PROJECT_DEPLOY:
                await this.handleProjectDeployMessage(message, panel);
                break;
            case WebViewType.SETTINGS_PANEL:
                await this.handleSettingsMessage(message, panel);
                break;
        }
    }
    /**
     * 实例向导消息处理
     */
    async handleInstanceWizardMessage(message, panel) {
        const { TomcatInstanceManager } = await Promise.resolve().then(() => __importStar(require("../services/TomcatInstanceManager")));
        const instanceManager = TomcatInstanceManager.getInstance();
        switch (message.command) {
            case "createInstance":
                try {
                    const instance = await instanceManager.createInstance(message.data);
                    panel.webview.postMessage({
                        command: "instanceCreated",
                        data: { success: true, instance: instance.toJSON() },
                    });
                    vscode.window.showInformationMessage(`Tomcat实例 "${instance.getName()}" 创建成功！`);
                }
                catch (error) {
                    panel.webview.postMessage({
                        command: "instanceCreated",
                        data: { success: false, error: String(error) },
                    });
                    vscode.window.showErrorMessage(`创建实例失败: ${error}`);
                }
                break;
            case "validatePorts":
                try {
                    const { PortManager } = await Promise.resolve().then(() => __importStar(require("../models/PortManager")));
                    const portManager = PortManager.getInstance();
                    const errors = await portManager.validatePortConfiguration(message.data);
                    panel.webview.postMessage({
                        command: "portsValidated",
                        data: { errors },
                    });
                }
                catch (error) {
                    console.error("Port validation error:", error);
                }
                break;
            case "suggestPorts":
                try {
                    const { PortManager } = await Promise.resolve().then(() => __importStar(require("../models/PortManager")));
                    const portManager = PortManager.getInstance();
                    const ports = await portManager.generateAvailablePortConfiguration();
                    panel.webview.postMessage({
                        command: "portsSuggested",
                        data: { ports },
                    });
                }
                catch (error) {
                    console.error("Port suggestion error:", error);
                }
                break;
            case "selectTomcatPath":
                try {
                    const result = await vscode.window.showOpenDialog({
                        canSelectFiles: false,
                        canSelectFolders: true,
                        canSelectMany: false,
                        openLabel: "选择Tomcat安装目录",
                        title: "选择Tomcat安装目录",
                    });
                    if (result && result[0]) {
                        panel.webview.postMessage({
                            command: "tomcatPathSelected",
                            data: { path: result[0].fsPath },
                        });
                    }
                }
                catch (error) {
                    console.error("Select Tomcat path error:", error);
                }
                break;
            case "selectJrePath":
                try {
                    const result = await vscode.window.showOpenDialog({
                        canSelectFiles: false,
                        canSelectFolders: true,
                        canSelectMany: false,
                        openLabel: "选择JRE目录",
                        title: "选择JRE目录",
                    });
                    if (result && result[0]) {
                        panel.webview.postMessage({
                            command: "jrePathSelected",
                            data: { path: result[0].fsPath },
                        });
                    }
                }
                catch (error) {
                    console.error("Select JRE path error:", error);
                }
                break;
            case "loadDefaultJrePath":
                try {
                    const { ConfigurationManager } = await Promise.resolve().then(() => __importStar(require("../services/ConfigurationManager")));
                    const configManager = ConfigurationManager.getInstance();
                    const globalConfig = configManager.getGlobalConfiguration();
                    // 尝试获取默认JRE路径
                    let defaultJrePath = globalConfig.defaultJrePath;
                    // 如果没有配置，尝试从环境变量获取
                    if (!defaultJrePath) {
                        defaultJrePath = process.env.JAVA_HOME || "";
                    }
                    // 如果还是没有，尝试常见的JRE路径
                    if (!defaultJrePath) {
                        const commonPaths = this.getCommonJrePaths();
                        for (const path of commonPaths) {
                            try {
                                const fs = await Promise.resolve().then(() => __importStar(require("fs")));
                                if (fs.existsSync(path)) {
                                    defaultJrePath = path;
                                    break;
                                }
                            }
                            catch (error) {
                                // 继续尝试下一个路径
                            }
                        }
                    }
                    panel.webview.postMessage({
                        command: "defaultJrePathLoaded",
                        data: { path: defaultJrePath || "" },
                    });
                }
                catch (error) {
                    console.error("Load default JRE path error:", error);
                    panel.webview.postMessage({
                        command: "defaultJrePathLoaded",
                        data: { path: "" },
                    });
                }
                break;
        }
    }
    /**
     * 实例配置消息处理
     */
    async handleInstanceConfigMessage(message, panel) {
        const { TomcatInstanceManager } = await Promise.resolve().then(() => __importStar(require("../services/TomcatInstanceManager")));
        const instanceManager = TomcatInstanceManager.getInstance();
        switch (message.command) {
            case "updateInstance":
                try {
                    await instanceManager.updateInstance(message.data.instanceId, message.data.updates);
                    panel.webview.postMessage({
                        command: "instanceUpdated",
                        data: { success: true },
                    });
                    vscode.window.showInformationMessage("实例配置更新成功！");
                }
                catch (error) {
                    panel.webview.postMessage({
                        command: "instanceUpdated",
                        data: { success: false, error: String(error) },
                    });
                    vscode.window.showErrorMessage(`更新配置失败: ${error}`);
                }
                break;
            case "loadInstance":
                try {
                    const instance = instanceManager.getInstance(message.data.instanceId);
                    if (instance) {
                        panel.webview.postMessage({
                            command: "instanceLoaded",
                            data: { instance: instance.toJSON() },
                        });
                    }
                }
                catch (error) {
                    console.error("Load instance error:", error);
                }
                break;
        }
    }
    /**
     * 项目部署消息处理
     */
    async handleProjectDeployMessage(message, panel) {
        const { ProjectDeploymentService } = await Promise.resolve().then(() => __importStar(require("../services/ProjectDeploymentService")));
        const { ProjectConfiguration } = await Promise.resolve().then(() => __importStar(require("../models/ProjectConfiguration")));
        const deploymentService = ProjectDeploymentService.getInstance();
        switch (message.command) {
            case "scanProjects":
                try {
                    const projects = await deploymentService.scanWorkspaceForProjects();
                    panel.webview.postMessage({
                        command: "projectsScanned",
                        data: { projects },
                    });
                }
                catch (error) {
                    console.error("Scan projects error:", error);
                }
                break;
            case "deployProject":
                try {
                    const projectConfig = ProjectConfiguration.createDefault(message.data.projectPath, message.data.projectName);
                    // 根据项目类型更新构建配置
                    if (message.data.projectType) {
                        const { ProjectType } = await Promise.resolve().then(() => __importStar(require("../models/ProjectConfiguration")));
                        const buildConfig = projectConfig.getConfiguration().build;
                        switch (message.data.projectType) {
                            case ProjectType.GRADLE:
                                buildConfig.type = ProjectType.GRADLE;
                                buildConfig.buildCommand = "./gradlew build";
                                buildConfig.outputDirectory = "build/libs";
                                break;
                            case ProjectType.PLAIN_JAVA:
                                buildConfig.type = ProjectType.PLAIN_JAVA;
                                buildConfig.buildCommand = "ant war";
                                buildConfig.outputDirectory = "dist";
                                buildConfig.autoBuild = false;
                                break;
                            // Maven是默认配置，不需要修改
                        }
                        projectConfig.updateConfiguration({ build: buildConfig });
                    }
                    projectConfig.setContextPath(message.data.contextPath);
                    const result = await deploymentService.deployProject(projectConfig, message.data.instanceId);
                    panel.webview.postMessage({
                        command: "projectDeployed",
                        data: { success: result.status === "success", result },
                    });
                    if (result.status === "success") {
                        vscode.window.showInformationMessage(`项目 "${message.data.projectName}" 部署成功！`);
                    }
                    else {
                        vscode.window.showErrorMessage(`部署失败: ${result.message}`);
                    }
                }
                catch (error) {
                    panel.webview.postMessage({
                        command: "projectDeployed",
                        data: { success: false, error: String(error) },
                    });
                    vscode.window.showErrorMessage(`部署失败: ${error}`);
                }
                break;
        }
    }
    /**
     * 设置面板消息处理
     */
    async handleSettingsMessage(message, panel) {
        const { ConfigurationManager } = await Promise.resolve().then(() => __importStar(require("../services/ConfigurationManager")));
        const configManager = ConfigurationManager.getInstance();
        switch (message.command) {
            case "updateSettings":
                try {
                    await configManager.updateGlobalConfiguration(message.data);
                    panel.webview.postMessage({
                        command: "settingsUpdated",
                        data: { success: true },
                    });
                    vscode.window.showInformationMessage("设置更新成功！");
                }
                catch (error) {
                    panel.webview.postMessage({
                        command: "settingsUpdated",
                        data: { success: false, error: String(error) },
                    });
                    vscode.window.showErrorMessage(`更新设置失败: ${error}`);
                }
                break;
            case "loadSettings":
                try {
                    const settings = configManager.getGlobalConfiguration();
                    panel.webview.postMessage({
                        command: "settingsLoaded",
                        data: { settings },
                    });
                }
                catch (error) {
                    console.error("Load settings error:", error);
                }
                break;
        }
    }
    /**
     * 获取实例向导HTML
     */
    getInstanceWizardHtml(webview, mediaUri, data) {
        const { HtmlTemplates } = require("./HtmlTemplates");
        return HtmlTemplates.getInstanceWizardHtml();
    }
    /**
     * 获取实例配置HTML
     */
    getInstanceConfigHtml(webview, mediaUri, data) {
        // TODO: 实现实例配置界面
        return "<html><body><h1>实例配置界面</h1><p>开发中...</p></body></html>";
    }
    /**
     * 获取项目部署HTML
     */
    getProjectDeployHtml(webview, mediaUri, data) {
        const { HtmlTemplates } = require("./HtmlTemplates");
        return HtmlTemplates.getProjectDeployHtml(data);
    }
    /**
     * 获取设置面板HTML
     */
    getSettingsPanelHtml(webview, mediaUri, data) {
        // TODO: 实现设置面板界面
        return "<html><body><h1>设置面板</h1><p>开发中...</p></body></html>";
    }
    /**
     * 获取常见的JRE路径
     */
    getCommonJrePaths() {
        const platform = process.platform;
        if (platform === "win32") {
            return [
                "C:\\Program Files\\Java\\jdk-11",
                "C:\\Program Files\\Java\\jdk-17",
                "C:\\Program Files\\Java\\jdk-21",
                "C:\\Program Files\\Java\\jre-11",
                "C:\\Program Files\\Java\\jre-17",
                "C:\\Program Files (x86)\\Java\\jdk-11",
                "C:\\Program Files (x86)\\Java\\jdk-17",
                "C:\\Program Files (x86)\\Java\\jre-11",
                "C:\\Program Files (x86)\\Java\\jre-17",
            ];
        }
        else if (platform === "darwin") {
            return [
                "/Library/Java/JavaVirtualMachines/jdk-11.jdk/Contents/Home",
                "/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home",
                "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home",
                "/System/Library/Java/JavaVirtualMachines/1.8.0.jdk/Contents/Home",
                "/usr/libexec/java_home",
            ];
        }
        else {
            return [
                "/usr/lib/jvm/java-11-openjdk",
                "/usr/lib/jvm/java-17-openjdk",
                "/usr/lib/jvm/java-21-openjdk",
                "/usr/lib/jvm/java-8-openjdk",
                "/usr/lib/jvm/default-java",
                "/opt/java/openjdk",
                "/usr/java/latest",
            ];
        }
    }
}
exports.WebViewManager = WebViewManager;
//# sourceMappingURL=WebViewManager.js.map