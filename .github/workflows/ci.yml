name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Compile TypeScript
      run: npm run compile
    
    - name: Package extension
      run: npm run package
      if: matrix.node-version == '18.x'
    
    - name: Upload VSIX artifact
      uses: actions/upload-artifact@v3
      if: matrix.node-version == '18.x'
      with:
        name: vscode-tomcat-manager.vsix
        path: '*.vsix'
