/* VSCode Tomcat Manager WebView样式 */

/* 这个文件为WebView提供额外的样式支持 */
/* 主要样式已经内联在HTML模板中 */

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 600px) {
    .form-row {
        flex-direction: column;
    }
    
    .port-grid {
        grid-template-columns: 1fr;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .button-group .button {
        width: 100%;
        margin-bottom: 10px;
    }
}
