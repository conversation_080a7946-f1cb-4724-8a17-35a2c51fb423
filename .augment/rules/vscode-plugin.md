---
type: "agent_requested"
description: "Example description"
---

# VS Code 插件开发 AI 提示词

以下是辅助开发 VS Code 插件的 AI 提示词，可以帮助开发者了解项目初始化、命令注册、API 使用、调试异常处理、插件打包与发布以及单元测试的相关内容。

1. 插件项目初始化与结构

   - 请生成一个 VS Code 插件项目的基本结构，包括 package.json、扩展入口文件（如 extension.ts）以及必要的配置文件。
   - 详细说明插件开发的核心目标，例如支持文件高亮、代码片段自动补全、调试支持等。

2. 接入 VS Code API

   - 请列出如何使用 VS Code 提供的 API，实现窗口操作、文本编辑、状态栏更新及命令注册。
   - 描述如何监听并处理用户命令，通过 API 完成对应的操作。

3. 配置与贡献点设置

   - 展示如何在 package.json 中配置扩展的贡献点（contributions），包括菜单、快捷键、代码片段和调试配置。
   - 提供示例代码，说明如何读取用户配置并监听文件变化及事件驱动机制。

4. 调试及异常处理机制

   - 描述如何为插件编写调试配置，并利用 VS Code 内置调试工具进行调试。
   - 提供示例代码，说明如何捕获异常并在 VS Code 窗口中友好地展示错误信息。

5. 插件打包与发布流程

   - 请说明如何使用 VSCE 工具打包插件并发布到 VS Code 插件市场。
   - 描述如何自动化版本管理和持续集成，确保插件稳定性。

6. 用户体验与交互设计

   - 讨论如何设计插件的用户交互界面，例如使用 Webview 构建自定义面板或仪表盘。
   - 指导如何实现实时状态提示和操作反馈，提升用户体验。

7. 单元测试与集成测试

   - 生成如何为 VS Code 插件编写单元测试和集成测试的详细步骤，并提供示例代码。
   - 包括常见错误处理和边界情况的代码示例及调试方法。

8. 项目文档与示例代码
   - 生成插件的 README 文件大纲，包含功能介绍、安装说明、常见问题及贡献指南。
   - 给出关键功能演示的示例项目，帮助新手快速上手插件开发。

请根据具体需求进行细化与调整，以便为开发者提供最有效的支持。
