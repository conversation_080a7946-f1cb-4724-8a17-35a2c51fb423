# 🔥 热部署功能实现

## 🎯 功能概述

实现了完整的热部署功能，当你修改项目文件时，系统会自动检测变化并部署到Tomcat，无需手动重新部署。

## ✨ 主要特性

### 1. 自动文件监听
- **实时监听**：监听项目目录下的文件变化
- **智能过滤**：只监听配置的文件扩展名
- **排除目录**：自动排除build目录、node_modules等
- **延迟部署**：文件变化后延迟1秒部署，避免频繁操作

### 2. 增量更新策略
- **静态文件**：直接复制到部署目录（HTML、CSS、JS、JSP等）
- **Java文件**：触发完整构建和部署（需要编译）
- **配置文件**：触发完整部署（XML、Properties等）

### 3. 智能路径处理
- **Maven项目**：自动处理`src/main/webapp/`和`src/main/resources/`路径
- **Gradle项目**：支持标准Gradle Web项目结构
- **相对路径**：正确计算文件在部署目录中的位置

## 🔧 技术实现

### 1. 文件监听服务
```typescript
// src/services/HotDeployService.ts
export class HotDeployService {
  private fileWatchers: Map<string, vscode.FileSystemWatcher> = new Map();
  private pendingTasks: Map<string, HotDeployTask> = new Map();
  private deploymentTimers: Map<string, NodeJS.Timeout> = new Map();

  startWatching(project: ProjectConfiguration): void {
    // 创建文件监听器
    const watchPattern = new vscode.RelativePattern(projectPath, '**/*');
    const watcher = vscode.workspace.createFileSystemWatcher(watchPattern);

    // 监听文件变化事件
    watcher.onDidCreate(uri => this.handleFileChange('created', uri, project));
    watcher.onDidChange(uri => this.handleFileChange('modified', uri, project));
    watcher.onDidDelete(uri => this.handleFileChange('deleted', uri, project));
  }
}
```

### 2. 增量更新实现
```typescript
// 静态文件直接复制
private async updateStaticFiles(project: ProjectConfiguration, instanceId: string, files: string[]): Promise<void> {
  const deployDir = path.join(config.instancePath, 'webapps', contextPath);
  
  for (const filePath of files) {
    // 计算相对路径
    let relativePath = path.relative(projectPath, filePath);
    
    // 处理Maven/Gradle项目的资源路径
    if (relativePath.startsWith('src/main/webapp/')) {
      relativePath = relativePath.substring('src/main/webapp/'.length);
    }
    
    const targetPath = path.join(deployDir, relativePath);
    fs.copyFileSync(filePath, targetPath);
  }
}

// Java文件触发完整构建
private async updateJavaClasses(project: ProjectConfiguration, instanceId: string, files: string[]): Promise<void> {
  // Java文件修改需要重新构建和部署
  const result = await this.deploymentService.deployProject(project, instanceId);
}
```

### 3. 部署集成
```typescript
// src/services/ProjectDeploymentService.ts
private async performDeployment(project: ProjectConfiguration, instanceId: string): Promise<DeploymentResult> {
  // ... 部署逻辑 ...
  
  // 4. 启动热部署监听（如果启用）
  if (project.isHotDeployEnabled()) {
    const hotDeployService = HotDeployService.getInstance();
    project.setTomcatInstanceId(instanceId);
    hotDeployService.startWatching(project);
  }
}
```

## 📋 配置说明

### 1. 热部署配置
```typescript
interface HotDeployConfiguration {
  enabled: boolean;                    // 是否启用热部署
  watchExtensions: string[];           // 监听的文件扩展名
  excludeDirectories: string[];        // 排除的目录
  deployDelay: number;                 // 部署延迟（毫秒）
  restartApp: boolean;                 // 是否重启应用
}
```

### 2. 默认配置
```typescript
hotDeploy: {
  enabled: true,
  watchExtensions: ['java', 'jsp', 'html', 'css', 'js', 'xml'],
  excludeDirectories: ['target', 'node_modules', '.git'],
  deployDelay: 1000,
  restartApp: false
}
```

### 3. 项目类型配置
- **Maven项目**：监听`src/main/webapp/`和`src/main/resources/`
- **Gradle项目**：监听`src/main/webapp/`和`src/main/resources/`
- **Plain Java项目**：监听项目根目录

## 🚀 使用方法

### 1. 启用热部署
1. 在项目部署界面中勾选"启用热部署"
2. 配置监听的文件扩展名和排除目录
3. 设置部署延迟时间
4. 部署项目到Tomcat实例

### 2. 自动监听
- 部署成功后，热部署监听自动启动
- 修改项目文件时会在控制台看到监听日志
- 文件变化后延迟指定时间自动部署

### 3. 监听状态
```
Started hot deploy watching for project: MyWebApp
File modified: /path/to/project/src/main/webapp/index.html
Updating 1 static files for project MyWebApp
Updated static file: index.html
Hot deploy completed for MyWebApp
```

## 📊 文件类型处理

### 1. 静态文件（增量更新）
- **HTML文件**：`.html` - 直接复制到部署目录
- **CSS文件**：`.css` - 直接复制到部署目录
- **JavaScript文件**：`.js` - 直接复制到部署目录
- **JSP文件**：`.jsp` - 直接复制到部署目录
- **图片文件**：`.png`, `.jpg`, `.gif`, `.svg` - 直接复制

### 2. Java文件（完整部署）
- **Java源码**：`.java` - 触发完整构建和部署
- **需要编译**：Java文件修改需要重新编译为class文件

### 3. 配置文件（完整部署）
- **XML配置**：`.xml` - 触发完整部署
- **Properties文件**：`.properties` - 触发完整部署
- **YAML配置**：`.yml`, `.yaml` - 触发完整部署

## 🔍 调试信息

### 1. 控制台日志
```
[HotDeploy] Started hot deploy watching for project: MyWebApp
[HotDeploy] File modified: /path/to/file.html
[HotDeploy] Updating 1 static files for project MyWebApp
[HotDeploy] Updated static file: index.html
[HotDeploy] Hot deploy completed for MyWebApp
```

### 2. VSCode通知
- **部署成功**：`Hot deploy completed for ProjectName`
- **部署失败**：`Hot deploy failed: Error message`

## ⚡ 性能优化

### 1. 延迟部署
- 文件变化后延迟1秒部署
- 避免频繁的文件变化触发多次部署
- 可以在配置中调整延迟时间

### 2. 增量更新
- 静态文件直接复制，无需重新构建
- 只有Java文件修改才触发完整构建
- 减少不必要的构建时间

### 3. 智能过滤
- 只监听配置的文件扩展名
- 自动排除build目录和临时文件
- 减少无效的文件变化监听

## 🛠️ 故障排除

### 1. 热部署未启动
- 检查项目配置中是否启用热部署
- 确认项目已成功部署到Tomcat实例
- 查看控制台是否有"Started hot deploy watching"日志

### 2. 文件变化未检测
- 检查文件扩展名是否在监听列表中
- 确认文件不在排除目录中
- 查看控制台是否有文件变化日志

### 3. 部署失败
- 检查Tomcat实例是否正在运行
- 确认部署目录权限正确
- 查看错误日志了解具体原因

## 📋 修改的文件

1. **src/services/HotDeployService.ts**
   - 实现真正的静态文件增量更新
   - 改进Java文件和配置文件处理
   - 添加详细的调试日志

2. **src/services/ProjectDeploymentService.ts**
   - 在部署成功后自动启动热部署监听
   - 设置项目的Tomcat实例ID关联

3. **src/extension.ts**
   - 在扩展停用时停止所有热部署监听

现在热部署功能已经完整实现，修改代码后会自动部署到Tomcat！
