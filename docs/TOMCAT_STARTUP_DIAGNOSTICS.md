# 🔧 Tomcat启动诊断功能

## 🐛 问题描述

用户反馈Tomcat部署成功后启动失败，且没有启动日志显示，无法诊断具体的失败原因。

## 🔧 解决方案

### 1. 实时日志收集

#### 问题
- 原来的启动过程没有捕获stdout和stderr输出
- 用户无法看到Tomcat的启动过程和错误信息
- 日志文件没有被正确写入

#### 修复
```typescript
// 创建日志收集
let startupLog = "";
let errorLog = "";

// 监听stdout
childProcess.stdout?.on("data", (data: Buffer) => {
  const output = data.toString();
  startupLog += output;
  console.log(`[${instanceId}] STDOUT:`, output);
  
  // 将日志写入文件
  this.writeToLogFile(config.logPath, "catalina.out", output);
});

// 监听stderr
childProcess.stderr?.on("data", (data: Buffer) => {
  const output = data.toString();
  errorLog += output;
  console.error(`[${instanceId}] STDERR:`, output);
  
  // 将错误日志写入文件
  this.writeToLogFile(config.logPath, "catalina.out", output);
});
```

### 2. 启动失败诊断

#### 自动诊断检查
```typescript
private async collectStartupDiagnostics(
  instance: TomcatInstance,
  startupLog: string,
  errorLog: string
): Promise<any> {
  const config = instance.getConfiguration();
  const diagnostics: any = {
    instanceId: instance.getId(),
    instanceName: instance.getName(),
    baseTomcatPath: config.baseTomcatPath,
    instancePath: config.instancePath,
    jrePath: config.jvm.jrePath,
    ports: config.ports,
    startupLog: startupLog.substring(0, 1000),
    errorLog: errorLog.substring(0, 1000),
    checks: {}
  };

  // 检查基础Tomcat路径
  diagnostics.checks.baseTomcatExists = fs.existsSync(config.baseTomcatPath);
  
  // 检查启动脚本
  const isWindows = process.platform === "win32";
  const scriptName = isWindows ? "catalina.bat" : "catalina.sh";
  const scriptPath = path.join(config.baseTomcatPath, "bin", scriptName);
  diagnostics.checks.startupScriptExists = fs.existsSync(scriptPath);

  // 检查JRE路径
  diagnostics.checks.jreExists = fs.existsSync(config.jvm.jrePath);

  // 检查实例目录
  diagnostics.checks.instancePathExists = fs.existsSync(config.instancePath);

  // 检查端口占用
  try {
    const httpPortAvailable = await this.portManager.isPortAvailable(config.ports.httpPort);
    diagnostics.checks.httpPortAvailable = httpPortAvailable;
  } catch (error) {
    diagnostics.checks.httpPortError = String(error);
  }

  // 检查配置文件
  const serverXmlPath = path.join(config.instancePath, "conf", "server.xml");
  diagnostics.checks.serverXmlExists = fs.existsSync(serverXmlPath);

  return diagnostics;
}
```

### 3. 用户友好的错误信息

#### 格式化错误消息
```typescript
private formatStartupError(error: any, diagnostics: any): string {
  let message = `Tomcat启动失败: ${error.message}\n\n`;
  
  message += `实例信息:\n`;
  message += `- 名称: ${diagnostics.instanceName}\n`;
  message += `- ID: ${diagnostics.instanceId}\n`;
  message += `- HTTP端口: ${diagnostics.ports.httpPort}\n\n`;

  message += `诊断结果:\n`;
  if (!diagnostics.checks.baseTomcatExists) {
    message += `❌ 基础Tomcat路径不存在: ${diagnostics.baseTomcatPath}\n`;
  }
  if (!diagnostics.checks.startupScriptExists) {
    message += `❌ 启动脚本不存在\n`;
  }
  if (!diagnostics.checks.jreExists) {
    message += `❌ JRE路径不存在: ${diagnostics.jrePath}\n`;
  }
  if (!diagnostics.checks.instancePathExists) {
    message += `❌ 实例目录不存在: ${diagnostics.instancePath}\n`;
  }
  if (!diagnostics.checks.serverXmlExists) {
    message += `❌ server.xml配置文件不存在\n`;
  }
  if (!diagnostics.checks.httpPortAvailable) {
    message += `❌ HTTP端口 ${diagnostics.ports.httpPort} 被占用\n`;
  }

  if (diagnostics.errorLog) {
    message += `\n错误日志:\n${diagnostics.errorLog}`;
  }

  message += `\n\n请检查配置并查看完整日志文件获取更多信息。`;

  return message;
}
```

### 4. 日志文件写入

#### 自动创建日志文件
```typescript
private writeToLogFile(logPath: string, fileName: string, content: string): void {
  try {
    // 确保日志目录存在
    if (!fs.existsSync(logPath)) {
      fs.mkdirSync(logPath, { recursive: true });
    }
    
    const logFilePath = path.join(logPath, fileName);
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${content}`;
    
    // 追加写入日志文件
    fs.appendFileSync(logFilePath, logEntry);
  } catch (error) {
    console.error("Failed to write to log file:", error);
  }
}
```

## ✅ 修复效果

### 修复前
- ❌ 启动失败时没有任何日志信息
- ❌ 用户无法知道具体的失败原因
- ❌ 需要手动检查各种配置和路径
- ❌ 调试困难，无法快速定位问题

### 修复后
- ✅ **实时日志收集**：捕获Tomcat启动过程的所有输出
- ✅ **自动诊断**：检查常见的配置问题（路径、端口、文件存在性等）
- ✅ **详细错误信息**：显示具体的失败原因和建议
- ✅ **日志文件写入**：自动创建和写入catalina.out日志文件
- ✅ **控制台输出**：在VSCode开发者控制台显示详细的调试信息

## 🔍 诊断检查项目

1. **基础Tomcat路径**：检查配置的Tomcat安装目录是否存在
2. **启动脚本**：检查catalina.sh/catalina.bat是否存在
3. **JRE路径**：检查配置的Java运行环境是否存在
4. **实例目录**：检查Tomcat实例工作目录是否存在
5. **配置文件**：检查server.xml等关键配置文件是否存在
6. **端口占用**：检查HTTP端口是否被其他进程占用

## 📋 修改的文件

1. **src/services/TomcatInstanceManager.ts**
   - 添加实时日志收集
   - 添加启动诊断功能
   - 添加错误信息格式化
   - 添加日志文件写入功能

2. **out/services/TomcatInstanceManager.js**
   - 同步更新编译后的JavaScript文件

## 🔄 使用方法

1. **查看启动日志**：
   - 右键点击Tomcat实例 → "显示日志"
   - 或者查看实例目录下的`logs/catalina.out`文件

2. **诊断启动问题**：
   - 启动失败时会自动显示详细的诊断信息
   - 检查VSCode开发者控制台获取更多调试信息

3. **常见问题解决**：
   - 根据错误信息检查相应的配置项
   - 确保所有路径和文件都正确存在
   - 检查端口是否被占用

现在当Tomcat启动失败时，你将能够看到详细的错误信息和诊断结果，帮助快速定位和解决问题！
