# 🔧 实例配置数据反显修复

## 🐛 问题描述

用户反馈点击"Configure"按钮后，配置页面打开但所有数据都没有正确反显，表单字段都是空的。

## 🔍 问题分析

### 1. 原始实现问题
原来的实现试图从WebView的URL参数中获取实例ID：

```javascript
// 错误的实现
const urlParams = new URLSearchParams(window.location.search);
const instanceId = urlParams.get('instanceId');
if (instanceId) {
    currentInstanceId = instanceId;
    sendMessage('loadInstance', { instanceId: instanceId });
}
```

**问题**：VSCode的WebView不是标准浏览器环境，无法通过URL参数传递数据。

### 2. 数据传递流程问题
- WebView创建时没有立即发送实例数据
- JavaScript等待URL参数但获取不到
- 导致无法加载实例配置数据

## 🔧 解决方案

### 1. 修改数据传递机制

#### WebViewManager改进
```typescript
// 在WebView创建后立即发送实例数据
if (type === WebViewType.INSTANCE_CONFIG && data?.instanceId) {
  // 延迟一点时间确保WebView已经完全加载
  setTimeout(() => {
    this.sendInstanceDataToPanel(panel, data.instanceId);
  }, 500);
}

// 新增发送实例数据的方法
private async sendInstanceDataToPanel(
  panel: vscode.WebviewPanel,
  instanceId: string
): Promise<void> {
  try {
    console.log("Sending instance data for ID:", instanceId);
    const { TomcatInstanceManager } = await import(
      "../services/TomcatInstanceManager"
    );
    const instanceManager = TomcatInstanceManager.getInstance();
    const instance = instanceManager.getInstance(instanceId);
    
    if (instance) {
      const instanceData = instance.toJSON();
      console.log("Instance data to send:", instanceData);
      panel.webview.postMessage({
        command: "instanceLoaded",
        data: { instance: instanceData },
      });
      console.log("Instance data sent successfully");
    } else {
      console.error("Instance not found:", instanceId);
    }
  } catch (error) {
    console.error("Failed to send instance data:", error);
  }
}
```

### 2. 简化JavaScript逻辑

#### 移除URL参数获取
```javascript
// 修改前：尝试从URL获取参数
const urlParams = new URLSearchParams(window.location.search);
const instanceId = urlParams.get('instanceId');
if (instanceId) {
    currentInstanceId = instanceId;
    sendMessage('loadInstance', { instanceId: instanceId });
}

// 修改后：等待消息自动发送
// 实例数据将通过消息自动发送，无需手动请求
```

#### 改进配置加载函数
```javascript
function loadInstanceConfig(instance) {
    console.log('Loading instance config:', instance);
    originalConfig = instance;
    currentInstanceId = instance.id; // ✅ 设置当前实例ID
    
    // 填充基本信息
    document.getElementById('name').value = instance.name || '';
    document.getElementById('description').value = instance.description || '';
    
    // 填充端口配置
    document.getElementById('httpPort').value = instance.ports.httpPort || '';
    document.getElementById('httpsPort').value = instance.ports.httpsPort || '';
    document.getElementById('ajpPort').value = instance.ports.ajpPort || '';
    document.getElementById('jmxPort').value = instance.ports.jmxPort || '';
    document.getElementById('shutdownPort').value = instance.ports.shutdownPort || '';
    
    // 填充JVM配置
    document.getElementById('jrePath').value = instance.jvm.jrePath || '';
    document.getElementById('minHeapSize').value = instance.jvm.minHeapSize || '';
    document.getElementById('maxHeapSize').value = instance.jvm.maxHeapSize || '';
    
    // 填充JVM参数
    if (instance.jvm.additionalArgs && instance.jvm.additionalArgs.length > 0) {
        document.getElementById('jvmArgs').value = instance.jvm.additionalArgs.join('\\n');
    }
    
    // 填充浏览器配置
    document.getElementById('browserType').value = instance.browser.type || 'default';
    document.getElementById('autoOpenBrowser').checked = instance.browser.autoOpen !== false;
    document.getElementById('defaultPage').value = instance.browser.defaultPage || '';
    document.getElementById('customPath').value = instance.browser.customPath || '';
    
    // 触发浏览器类型变化事件
    const browserTypeElement = document.getElementById('browserType');
    if (browserTypeElement) {
        browserTypeElement.dispatchEvent(new Event('change'));
    }
}
```

### 3. 增强消息处理

#### 添加调试日志
```javascript
function handleMessage(message) {
    console.log('Received message:', message);
    switch (message.command) {
        case 'instanceLoaded':
            console.log('Instance loaded message received:', message.data);
            if (message.data.instance) {
                loadInstanceConfig(message.data.instance);
            } else {
                console.error('No instance data in instanceLoaded message');
            }
            break;
        // ... 其他消息处理
    }
}
```

## ✅ 修复效果

### 修复前
- ❌ 配置页面打开但数据为空
- ❌ 无法获取实例ID
- ❌ 表单字段没有预填充

### 修复后
- ✅ **自动数据加载**：WebView创建后立即发送实例数据
- ✅ **正确数据反显**：所有配置字段正确填充
- ✅ **调试信息完善**：便于排查问题

## 🔄 数据流程

### 新的数据流程
1. **用户操作**：右键实例 → 选择"配置实例"
2. **命令处理**：extension.ts中的configureInstance命令
3. **WebView创建**：WebViewManager.createOrShowPanel()
4. **HTML加载**：设置WebView的HTML内容
5. **数据发送**：延迟500ms后自动发送实例数据
6. **消息接收**：WebView接收instanceLoaded消息
7. **数据填充**：loadInstanceConfig()填充表单字段

### 关键时序
```
WebView创建 → HTML加载 → 延迟500ms → 发送实例数据 → 接收消息 → 填充表单
```

## 🔍 调试方法

### 1. 检查控制台日志
打开VSCode开发者工具，查看以下日志：
- `Sending instance data for ID: [instanceId]`
- `Instance data to send: [instanceData]`
- `Instance data sent successfully`
- `Received message: [message]`
- `Instance loaded message received: [data]`
- `Loading instance config: [instance]`

### 2. 验证数据传递
确认以下步骤都正常：
1. 实例ID正确传递到WebViewManager
2. 实例数据成功获取
3. 消息成功发送到WebView
4. WebView正确接收和处理消息
5. 表单字段正确填充

## 📋 修改的文件

1. **src/webview/WebViewManager.ts**
   - 添加WebView创建后的数据发送逻辑
   - 新增sendInstanceDataToPanel方法
   - 添加调试日志

2. **src/webview/HtmlTemplates.ts**
   - 移除URL参数获取逻辑
   - 改进loadInstanceConfig函数
   - 添加调试日志到消息处理

## 🎯 测试步骤

1. **创建Tomcat实例**（如果还没有）
2. **右键实例** → 选择"配置实例"
3. **检查配置页面**：
   - 实例名称应该正确显示
   - 端口配置应该反显当前值
   - JVM配置应该显示当前设置
   - 浏览器设置应该正确反显

4. **检查控制台**：
   - 打开VSCode开发者工具
   - 查看是否有相关的调试日志
   - 确认没有错误信息

现在配置页面应该能够正确显示所有实例数据了！
