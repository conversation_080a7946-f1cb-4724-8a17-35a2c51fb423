# VSCode Tomcat Manager 使用指南

## 快速开始

### 1. 安装和配置

1. **安装插件**
   - 在VSCode中按 `Ctrl+Shift+X` 打开扩展面板
   - 搜索 "Tomcat Manager"
   - 点击安装

2. **配置基础Tomcat路径**
   - 打开VSCode设置 (`Ctrl+,`)
   - 搜索 "tomcat manager"
   - 设置 "Base Tomcat Path" 为你的Tomcat安装目录
   - 例如：`/usr/local/tomcat` 或 `C:\apache-tomcat-9.0.65`

### 2. 创建第一个Tomcat实例

1. **打开Tomcat Manager视图**
   - 在资源管理器面板中找到 "Tomcat Manager" 视图
   - 如果没有看到，请确保插件已正确安装

2. **添加新实例**
   - 点击 "Tomcat Manager" 视图中的 "+" 按钮
   - 按照向导填写实例信息：
     - **实例名称**：例如 "MyApp-Dev"
     - **基础Tomcat路径**：选择你的Tomcat安装目录
     - **JRE路径**：选择Java运行环境（可选，默认使用JAVA_HOME）
     - **描述**：实例的描述信息（可选）

3. **启动实例**
   - 在创建的实例上右键点击
   - 选择 "Start" 启动实例
   - 状态指示器会显示启动进度

### 3. 部署Java Web项目

1. **准备项目**
   - 确保你的工作区中有Java Web项目
   - 支持Maven、Gradle或普通Java项目

2. **部署项目**
   - 右键点击运行中的Tomcat实例
   - 选择 "Deploy Project"
   - 从列表中选择要部署的项目
   - 配置上下文路径（默认为ROOT）
   - 点击确认开始部署

3. **访问应用**
   - 部署成功后，右键点击实例
   - 选择 "Open in Browser" 在浏览器中打开应用
   - 或者点击部署的应用直接访问

## 高级功能

### 端口配置

每个Tomcat实例可以配置独立的端口：

- **HTTP端口**：默认8080，用于HTTP访问
- **HTTPS端口**：默认8443，用于HTTPS访问
- **AJP端口**：默认8009，用于与Web服务器通信
- **JMX端口**：默认9999，用于远程监控
- **管理端口**：默认8005，用于Tomcat管理

**配置方法**：
1. 右键点击实例 → "Configure"
2. 选择 "Update Ports"
3. 修改所需端口
4. 保存配置（如果实例正在运行会自动重启）

### 热部署配置

热部署允许在不重启Tomcat的情况下自动更新代码：

**启用热部署**：
1. 在项目部署时选择启用热部署
2. 或在项目配置中启用

**监听的文件类型**：
- `.java` - Java源文件
- `.jsp` - JSP页面
- `.html`, `.css`, `.js` - 静态资源
- `.xml`, `.properties` - 配置文件

**排除目录**：
- `target` - Maven构建目录
- `build` - Gradle构建目录
- `node_modules` - Node.js依赖
- `.git` - Git版本控制

### 浏览器集成

**支持的浏览器**：
- 系统默认浏览器
- Google Chrome
- Mozilla Firefox
- Microsoft Edge
- Safari（仅macOS）
- 自定义浏览器

**配置方法**：
1. 打开VSCode设置
2. 搜索 "tomcat manager browser"
3. 选择默认浏览器类型
4. 如选择自定义，需指定浏览器可执行文件路径

### JVM配置

为不同实例配置不同的JVM参数：

**可配置项**：
- JRE路径
- 最小堆内存（-Xms）
- 最大堆内存（-Xmx）
- 其他JVM参数

**配置方法**：
1. 右键点击实例 → "Configure"
2. 选择 "Update JVM Settings"
3. 修改相关参数
4. 保存配置

## 常用操作

### 实例管理

| 操作 | 方法 |
|------|------|
| 启动实例 | 右键 → Start |
| 停止实例 | 右键 → Stop |
| 重启实例 | 右键 → Restart |
| 查看日志 | 右键 → Show Logs |
| 配置实例 | 右键 → Configure |
| 删除实例 | 右键 → Delete |

### 批量操作

- **启动所有实例**：状态栏中点击 "Start All"
- **停止所有实例**：状态栏中点击 "Stop All"

### 状态监控

**状态指示器**：
- 🟢 运行中
- 🔴 已停止
- 🟡 启动中
- 🟠 停止中
- ❌ 错误状态

**状态栏信息**：
- 实例总数
- 运行中实例数
- 端口使用情况
- 快速操作按钮

## 故障排除

### 常见问题

**1. 端口被占用**
- 检查端口使用情况
- 修改实例端口配置
- 停止占用端口的其他程序

**2. 构建失败**
- 检查构建命令是否正确
- 确认Maven/Gradle已正确安装
- 查看构建输出日志

**3. 热部署不工作**
- 确认热部署已启用
- 检查文件扩展名配置
- 验证排除目录设置

**4. 浏览器无法打开**
- 检查浏览器配置
- 验证自定义浏览器路径
- 确认自动打开功能已启用

### 调试模式

启用调试日志：
1. 打开VSCode设置
2. 搜索 "tomcat manager debug"
3. 启用调试模式
4. 查看输出面板中的详细日志

### 获取帮助

- 📖 查看完整文档：[README.md](README.md)
- 🐛 报告问题：GitHub Issues
- 💬 讨论交流：GitHub Discussions

## 最佳实践

1. **实例命名**：使用有意义的名称，如 "ProjectName-Environment"
2. **端口管理**：为不同环境使用不同的端口范围
3. **热部署**：仅在开发环境启用，生产环境建议关闭
4. **资源监控**：定期检查实例状态和资源使用情况
5. **配置备份**：定期备份实例和项目配置

---

**享受使用VSCode Tomcat Manager进行Java Web开发！** 🚀
