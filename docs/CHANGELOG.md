# Change Log

All notable changes to the "Tomcat Manager" extension will be documented in this file.

## [1.0.0] - 2024-01-15

### Added
- 🎉 Initial release of VSCode Tomcat Manager
- 🚀 Multi-instance Tomcat management
- 🔧 Advanced port configuration with conflict detection
- 📦 Project deployment for Maven, Gradle, and plain Java projects
- 🔥 Hot deployment with file watching
- 🌐 Browser integration with multiple browser support
- ⚙️ JRE management for different instances
- 📊 Real-time status monitoring
- 🎯 Context path configuration (including ROOT deployment)
- 📝 Comprehensive logging and error handling

### Features

#### Core Functionality
- Create multiple Tomcat instances from a single base installation
- Independent configuration for each instance (ports, JVM, browser settings)
- Start, stop, and restart instances individually or in batch
- Real-time status monitoring with visual indicators

#### Port Management
- Automatic port conflict detection
- Configure HTTP (default: 8080), HTTPS (default: 8443), AJP (default: 8009), JMX (default: 9999), and shutdown (default: 8005) ports
- Smart port allocation for new instances
- Port usage monitoring and reporting

#### Project Deployment
- Support for Maven projects (automatic detection via pom.xml)
- Support for Gradle projects (automatic detection via build.gradle)
- Support for plain Java web projects
- Configurable context paths (ROOT or custom paths like /myapp)
- One-click deployment with automatic building
- Deploy multiple projects to the same instance

#### Hot Deployment
- Real-time file watching for automatic redeployment
- Configurable file extensions (.java, .jsp, .html, .css, .js, .xml, .properties)
- Configurable exclusion patterns (target, node_modules, .git)
- Incremental updates for static files
- Smart deployment delays to batch changes
- Configurable restart behavior

#### Browser Integration
- Support for multiple browsers:
  - System default browser
  - Google Chrome
  - Mozilla Firefox
  - Microsoft Edge
  - Safari (macOS only)
  - Custom browser with configurable path
- Automatic browser launch on Tomcat startup
- Configurable startup pages
- Support for incognito/private mode
- New window options

#### JRE Management
- Different JRE versions for different instances
- Automatic JRE detection from system
- Custom JVM parameters per instance
- Memory configuration (min/max heap size)
- Additional JVM arguments support

#### User Interface
- Tree view in Explorer panel showing:
  - Tomcat instances with status indicators
  - Deployed applications
  - Instance configuration details
- Status bar integration showing:
  - Total instances count
  - Running instances count
  - Port usage information
  - Quick action buttons
- Context menus for all operations
- Configuration wizards for easy setup

#### Configuration Management
- Global settings for default paths and preferences
- Instance-level configuration storage
- Project-level configuration
- Workspace-level configuration
- Import/export configuration support
- Configuration backup and restore

### Commands
- `tomcatManager.addTomcatInstance` - Create a new Tomcat instance
- `tomcatManager.startInstance` - Start a Tomcat instance
- `tomcatManager.stopInstance` - Stop a Tomcat instance
- `tomcatManager.restartInstance` - Restart a Tomcat instance
- `tomcatManager.deleteInstance` - Delete a Tomcat instance
- `tomcatManager.deployProject` - Deploy a project to an instance
- `tomcatManager.openInBrowser` - Open application in browser
- `tomcatManager.showLogs` - View Tomcat logs
- `tomcatManager.configureInstance` - Modify instance settings
- `tomcatManager.refresh` - Refresh the tree view
- `tomcatManager.startAllInstances` - Start all stopped instances
- `tomcatManager.stopAllInstances` - Stop all running instances

### Settings
- `tomcatManager.baseTomcatPath` - Path to base Tomcat installation
- `tomcatManager.defaultJrePath` - Default JRE path for instances
- `tomcatManager.defaultBrowser` - Default browser for opening applications
- `tomcatManager.customBrowserPath` - Path to custom browser executable
- `tomcatManager.autoOpenBrowser` - Auto-open browser when starting Tomcat
- `tomcatManager.portRange` - Default port ranges for new instances

### Technical Details
- Built with TypeScript for type safety
- Comprehensive error handling and validation
- Cross-platform support (Windows, macOS, Linux)
- Efficient file watching with debouncing
- Asynchronous operations for better performance
- Extensive logging for debugging
- Unit tests for core functionality

### Dependencies
- `xml2js` - XML parsing for Tomcat configuration
- `portfinder` - Port availability checking
- `tree-kill` - Process management

### Known Limitations
- Hot class replacement depends on JRE capabilities
- Some advanced Tomcat features may require manual configuration
- Large projects may take longer to deploy initially

### Compatibility
- VSCode 1.74.0 or higher
- Java 8 or higher
- Tomcat 7.x, 8.x, 9.x, 10.x
- Windows 10+, macOS 10.14+, Ubuntu 18.04+

---

## Future Releases

### Planned Features for v1.1.0
- [ ] Docker integration for containerized Tomcat instances
- [ ] Remote Tomcat management
- [ ] Advanced logging viewer with filtering
- [ ] Performance monitoring integration
- [ ] Cluster management support
- [ ] SSL certificate management
- [ ] Database connection pool monitoring
- [ ] Application metrics dashboard

### Planned Features for v1.2.0
- [ ] Integration with popular Java frameworks (Spring Boot, etc.)
- [ ] Automated testing integration
- [ ] CI/CD pipeline integration
- [ ] Advanced security configuration
- [ ] Load balancer configuration
- [ ] Backup and restore functionality
- [ ] Template-based instance creation
- [ ] Multi-workspace support

---

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).
