# 🔍 文件夹选择功能调试

## 🐛 问题描述

用户反馈在新增Tomcat实例时，点击"📁 选择文件夹"按钮没有反应。

## 🔧 调试改进

### 1. 添加前端调试日志

#### JavaScript函数调试
```javascript
function selectTomcatPath() {
    console.log('selectTomcatPath clicked');  // ✅ 新增：确认按钮点击
    sendMessage('selectTomcatPath', {});
}

function sendMessage(command, data) {
    console.log('Sending message:', command, data);  // ✅ 新增：确认消息发送
    if (typeof vscode !== 'undefined') {
        vscode.postMessage({ command, data });
    } else {
        console.error('vscode object is not available');  // ✅ 新增：检查VSCode API
        showAlert('VSCode API不可用，请重新打开面板', 'error');
    }
}
```

#### 消息接收调试
```javascript
case 'tomcatPathSelected':
    console.log('tomcatPathSelected message received:', message.data);  // ✅ 新增
    if (message.data.path) {
        document.getElementById('baseTomcatPath').value = message.data.path;
        showAlert('Tomcat路径已选择: ' + message.data.path, 'success');
    } else {
        console.log('No path in tomcatPathSelected message');  // ✅ 新增
    }
    break;
```

### 2. 添加后端调试日志

#### WebViewManager消息处理调试
```typescript
case "selectTomcatPath":
  try {
    console.log("selectTomcatPath message received");  // ✅ 新增：确认消息接收
    const result = await vscode.window.showOpenDialog({
      canSelectFiles: false,
      canSelectFolders: true,
      canSelectMany: false,
      openLabel: "选择Tomcat安装目录",
      title: "选择Tomcat安装目录",
    });

    console.log("Dialog result:", result);  // ✅ 新增：确认对话框结果
    if (result && result[0]) {
      console.log("Sending tomcatPathSelected message with path:", result[0].fsPath);  // ✅ 新增
      panel.webview.postMessage({
        command: "tomcatPathSelected",
        data: { path: result[0].fsPath },
      });
    } else {
      console.log("No folder selected or dialog cancelled");  // ✅ 新增
    }
  } catch (error) {
    console.error("Select Tomcat path error:", error);
    vscode.window.showErrorMessage(`选择文件夹失败: ${error}`);  // ✅ 新增：用户错误提示
  }
```

## 🔍 调试步骤

### 1. 检查前端功能
1. **打开VSCode开发者工具**：
   - 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows/Linux)
   - 输入 "Developer: Toggle Developer Tools"
   - 打开开发者控制台

2. **测试按钮点击**：
   - 点击"📁 选择文件夹"按钮
   - 在控制台查看是否出现：`selectTomcatPath clicked`

3. **检查消息发送**：
   - 查看是否出现：`Sending message: selectTomcatPath {}`
   - 如果没有，说明按钮点击事件有问题

4. **检查VSCode API**：
   - 如果出现"vscode object is not available"，说明WebView环境有问题

### 2. 检查后端处理
1. **查看VSCode输出面板**：
   - 打开VSCode输出面板
   - 选择"Tomcat Manager"通道
   - 查看是否有相关日志

2. **检查消息接收**：
   - 查看是否出现：`selectTomcatPath message received`
   - 如果没有，说明消息传递有问题

3. **检查对话框**：
   - 查看是否出现：`Dialog result: ...`
   - 如果没有，说明文件对话框没有正常打开

### 3. 检查消息回传
1. **确认路径选择**：
   - 选择文件夹后查看：`Sending tomcatPathSelected message with path: ...`

2. **确认前端接收**：
   - 查看是否出现：`tomcatPathSelected message received: ...`

3. **确认UI更新**：
   - 检查输入框是否填入了选择的路径
   - 查看是否显示成功提示

## 🔧 可能的问题和解决方案

### 1. 按钮点击无响应
**可能原因**：
- JavaScript错误导致函数未定义
- 按钮HTML结构问题
- 事件绑定失败

**解决方案**：
```html
<!-- 确认按钮HTML正确 -->
<button type="button" class="button button-secondary" onclick="selectTomcatPath()" style="white-space: nowrap;">
    📁 选择文件夹
</button>
```

### 2. VSCode API不可用
**可能原因**：
- WebView环境初始化失败
- 扩展激活问题

**解决方案**：
- 重新打开WebView面板
- 重启VSCode
- 检查扩展是否正确激活

### 3. 消息传递失败
**可能原因**：
- WebView和扩展之间的通信中断
- 消息格式错误

**解决方案**：
```javascript
// 确认消息格式正确
vscode.postMessage({ 
    command: 'selectTomcatPath', 
    data: {} 
});
```

### 4. 文件对话框不显示
**可能原因**：
- VSCode权限问题
- 操作系统文件访问限制

**解决方案**：
```typescript
// 确认对话框配置正确
const result = await vscode.window.showOpenDialog({
    canSelectFiles: false,    // 只选择文件夹
    canSelectFolders: true,   // 允许选择文件夹
    canSelectMany: false,     // 单选
    openLabel: "选择Tomcat安装目录",
    title: "选择Tomcat安装目录",
});
```

## 📋 调试清单

### 前端检查
- [ ] 按钮点击是否触发`selectTomcatPath()`函数
- [ ] `sendMessage()`函数是否正常执行
- [ ] VSCode API (`vscode`对象) 是否可用
- [ ] 消息是否成功发送到后端

### 后端检查
- [ ] `selectTomcatPath`消息是否被接收
- [ ] 文件选择对话框是否正常打开
- [ ] 用户是否选择了文件夹
- [ ] 回传消息是否成功发送

### UI更新检查
- [ ] `tomcatPathSelected`消息是否被前端接收
- [ ] 输入框是否更新为选择的路径
- [ ] 是否显示成功提示消息

## 🎯 测试步骤

1. **重新加载扩展**：
   - 按 `Cmd+Shift+P` → "Developer: Reload Window"

2. **打开创建实例面板**：
   - 右键Tomcat视图 → "创建实例"

3. **打开开发者工具**：
   - `Cmd+Shift+P` → "Developer: Toggle Developer Tools"

4. **测试文件夹选择**：
   - 点击"📁 选择文件夹"按钮
   - 观察控制台日志
   - 选择一个文件夹
   - 确认路径是否填入输入框

## 📋 修改的文件

1. **src/webview/HtmlTemplates.ts**
   - 添加`selectTomcatPath()`函数调试日志
   - 添加`sendMessage()`函数调试和错误处理
   - 添加`tomcatPathSelected`消息处理调试

2. **src/webview/WebViewManager.ts**
   - 添加`selectTomcatPath`消息处理调试日志
   - 添加文件对话框结果调试
   - 添加错误处理和用户提示

现在通过这些调试日志，我们可以准确定位文件夹选择功能的问题所在！
