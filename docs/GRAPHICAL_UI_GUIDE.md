# 🎨 VSCode Tomcat Manager 图形化界面使用指南

现在VSCode Tomcat Manager插件提供了完全图形化的操作界面，无需使用命令行！

## 🚀 图形化功能概览

### 1. 创建Tomcat实例 - 可视化向导

**启动方式：**
- 点击Tomcat Manager视图中的 "+" 按钮
- 或使用命令面板：`Ctrl+Shift+P` → "Tomcat Manager: Add Instance"

**向导界面包含：**

#### 📋 基本信息配置
- **实例名称**：为实例指定有意义的名称（如：MyApp-Dev）
- **描述**：可选的详细描述
- **基础Tomcat路径**：选择Tomcat安装目录

#### 🔌 智能端口配置
- **可视化端口设置**：HTTP、HTTPS、AJP、JMX、管理端口
- **一键自动分配**：点击"自动分配端口"按钮，系统自动找到可用端口
- **实时端口验证**：输入端口后自动检查是否可用
- **端口冲突提示**：清晰显示端口冲突信息

#### ⚙️ JVM参数配置
- **JRE路径选择**：可视化选择Java运行环境
- **内存设置**：拖拽或输入最小/最大堆内存
- **参数预设**：常用JVM参数模板

#### 🌐 浏览器集成设置
- **浏览器类型选择**：下拉菜单选择浏览器
- **自动打开开关**：一键开启/关闭自动打开功能
- **启动页面配置**：设置默认打开的页面

### 2. 项目部署 - 三步向导

**启动方式：**
- 右键点击Tomcat实例 → "Deploy Project"

**三步部署流程：**

#### 步骤1：选择项目 📁
- **自动扫描**：点击"扫描项目"自动发现工作区中的Java Web项目
- **项目列表**：可视化显示项目名称、路径、类型
- **项目类型标识**：Maven、Gradle、Plain Java项目一目了然
- **点击选择**：直接点击项目卡片进行选择

#### 步骤2：配置部署 ⚙️
- **上下文路径设置**：
  - 下拉选择"ROOT"（根路径部署）
  - 或选择"自定义路径"并输入（如：/myapp）
- **热部署配置**：
  - 开关按钮启用/禁用热部署
  - 可视化配置监听文件类型
  - 设置排除目录
  - 调整部署延迟时间

#### 步骤3：部署进度 📊
- **实时进度显示**：可视化显示部署进度
- **构建日志查看**：实时显示Maven/Gradle构建输出
- **部署结果反馈**：成功/失败状态清晰显示
- **错误信息展示**：详细的错误信息和解决建议

### 3. 实例管理界面

#### 🎛️ 实例配置面板
- **标签页设计**：基本信息、端口配置、JVM设置、浏览器配置
- **实时预览**：配置更改实时预览效果
- **配置验证**：输入验证和错误提示
- **一键应用**：配置更改一键应用

#### 📊 状态监控面板
- **实例状态**：可视化状态指示器（运行中🟢、已停止🔴、启动中🟡）
- **端口使用情况**：端口占用状态图表
- **资源监控**：内存使用、CPU占用等信息
- **日志查看器**：内置日志查看器，支持过滤和搜索

### 4. 全局设置面板

**启动方式：**
- 点击Tomcat Manager视图标题栏的设置按钮 ⚙️
- 或使用命令：`Tomcat Manager: Open Settings`

**设置内容：**
- **默认路径配置**：Tomcat安装路径、JRE路径
- **端口范围设置**：各类端口的默认起始值
- **浏览器偏好**：默认浏览器、启动选项
- **热部署全局设置**：默认监听文件类型、排除模式
- **主题和外观**：界面主题、字体大小等

## 🎯 使用演示

### 快速开始：5分钟创建并部署

1. **创建实例（2分钟）**
   ```
   点击 "+" → 填写实例名称 → 点击"自动分配端口" → 点击"创建实例"
   ```

2. **部署项目（3分钟）**
   ```
   右键实例 → "Deploy Project" → 点击"扫描项目" → 选择项目 → 
   选择"ROOT"部署 → 启用热部署 → 点击"开始部署"
   ```

3. **访问应用**
   ```
   部署成功后自动打开浏览器，或点击实例 → "Open in Browser"
   ```

### 高级配置示例

#### 多环境部署配置
```
开发环境：MyApp-Dev (端口8080)
测试环境：MyApp-Test (端口8081)  
生产环境：MyApp-Prod (端口8082)
```

#### 热部署精细配置
```
监听文件：java, jsp, html, css, js
排除目录：target, node_modules, .git
部署延迟：1000ms
重启策略：仅配置文件变化时重启
```

## 🔧 界面特性

### 响应式设计
- **自适应布局**：支持不同屏幕尺寸
- **暗色主题**：完美适配VSCode暗色主题
- **高对比度**：支持高对比度模式

### 交互体验
- **实时反馈**：操作结果即时显示
- **进度指示**：长时间操作显示进度条
- **错误处理**：友好的错误信息和解决建议
- **快捷操作**：常用操作一键完成

### 数据持久化
- **配置自动保存**：所有配置自动保存到工作区
- **状态恢复**：重启VSCode后状态自动恢复
- **配置导入导出**：支持配置的备份和迁移

## 🎨 界面截图说明

### 创建实例向导
```
┌─────────────────────────────────────┐
│ 🚀 创建Tomcat实例                    │
├─────────────────────────────────────┤
│ 基本信息                             │
│ ┌─────────────────────────────────┐ │
│ │ 实例名称: [MyApp-Dev________] │ │
│ │ 描述: [开发环境实例_________] │ │
│ │ Tomcat路径: [/usr/local/tomcat] │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 端口配置                [🎯自动分配] │
│ ┌─────┬─────┬─────┬─────┬─────┐ │
│ │HTTP │HTTPS│ AJP │ JMX │管理 │ │
│ │8080 │8443 │8009 │9999 │8005 │ │
│ └─────┴─────┴─────┴─────┴─────┘ │
│                                     │
│ [创建实例] [取消]                    │
└─────────────────────────────────────┘
```

### 项目部署向导
```
┌─────────────────────────────────────┐
│ 📦 部署项目到 MyApp-Dev              │
├─────────────────────────────────────┤
│ [选择项目] [配置部署] [部署进度]     │
│                                     │
│ 工作区项目              [🔍扫描项目] │
│ ┌─────────────────────────────────┐ │
│ │ ✓ demo-webapp                   │ │
│ │   /path/to/demo-webapp          │ │
│ │   [Maven]                       │ │
│ ├─────────────────────────────────┤ │
│ │   my-spring-app                 │ │
│ │   /path/to/spring-app           │ │
│ │   [Gradle]                      │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [下一步] [取消]                      │
└─────────────────────────────────────┘
```

## 🚀 开始使用

1. **启动扩展开发主机**
   ```bash
   cd "tomcat and tomee"
   code .
   # 按F5启动扩展开发主机
   ```

2. **在新窗口中体验图形化界面**
   - 查看左侧"TOMCAT MANAGER"视图
   - 点击"+"按钮创建实例
   - 体验完整的图形化操作流程

3. **享受可视化的Tomcat管理体验！**

---

**现在您可以完全通过图形化界面管理Tomcat，告别复杂的命令行操作！** 🎉
