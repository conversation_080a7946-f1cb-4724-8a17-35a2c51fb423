# 🚀 Exploded WAR部署重构

## 🎯 问题分析

用户反馈IDEA的Tomcat插件热部署很稳定，而我们的实现经常出现403、404错误。经过分析发现关键差异：

### IDEA的做法
- 使用**Exploded WAR**部署（解压的WAR目录）
- 热部署时直接替换目录中的单个文件
- Tomcat立即检测到文件变化并生效
- 不需要重新打包和解压WAR文件

### 我们之前的做法
- 使用**打包的WAR文件**部署
- 热部署时重新打包整个WAR文件
- Tomcat需要重新解压WAR文件
- 在解压过程中会出现403、404等状态

## 🔧 解决方案

### 1. 改为Exploded WAR部署

#### 修改部署逻辑
```typescript
// 之前：部署打包的WAR文件
deployPath = path.join(config.instancePath, "webapps", "ROOT.war");
await this.copyFile(warPath, deployPath);

// 现在：部署exploded WAR目录
deployDir = path.join(config.instancePath, "webapps", "ROOT");
fs.mkdirSync(deployDir, { recursive: true });
await this.extractWarFile(warPath, deployDir);
```

#### 实现WAR文件解压
```typescript
private async extractWarFile(warPath: string, targetDir: string): Promise<void> {
  const yauzl = require('yauzl');
  
  return new Promise((resolve, reject) => {
    yauzl.open(warPath, { lazyEntries: true }, (err: any, zipfile: any) => {
      // 解压WAR文件到目标目录
      zipfile.on('entry', (entry: any) => {
        if (/\/$/.test(entry.fileName)) {
          // 目录
          fs.mkdirSync(path.join(targetDir, entry.fileName), { recursive: true });
        } else {
          // 文件
          zipfile.openReadStream(entry, (err: any, readStream: any) => {
            const writeStream = fs.createWriteStream(path.join(targetDir, entry.fileName));
            readStream.pipe(writeStream);
          });
        }
      });
    });
  });
}
```

### 2. 重新设计热部署逻辑

#### 真正的增量更新
```typescript
// 现在可以进行真正的增量更新
private async performHotDeploy(project: ProjectConfiguration, instanceId: string): Promise<void> {
  console.log(`Performing incremental hot deploy...`);

  try {
    await this.performIncrementalUpdate(project, instanceId);
  } catch (error) {
    // 如果增量更新失败，回退到完整部署
    console.warn(`Incremental update failed, falling back to full deployment:`, error);
    const result = await this.deploymentService.deployProject(project, instanceId);
  }
}
```

#### 直接文件替换
```typescript
private async updateStaticFilesToExplodedWar(
  project: ProjectConfiguration,
  instanceId: string,
  files: string[]
): Promise<void> {
  // 获取exploded WAR目录
  const deployDir = path.join(config.instancePath, "webapps", contextName);
  
  // 直接复制文件到exploded WAR目录
  for (const filePath of files) {
    const targetPath = path.join(deployDir, relativePath);
    fs.copyFileSync(filePath, targetPath);
    console.log(`✅ Hot deployed static file: ${relativePath}`);
  }
}
```

## ✅ 改进效果

### 部署方式对比

| 方面 | 打包WAR部署 | Exploded WAR部署 |
|------|-------------|------------------|
| **部署过程** | 复制WAR文件 → Tomcat解压 | 直接解压到目录 |
| **热部署** | 重新打包 → 重新解压 | 直接替换文件 |
| **访问稳定性** | 解压期间403/404 | 立即生效，无中断 |
| **部署速度** | 慢（需要解压） | 快（直接替换） |
| **IDEA兼容性** | 不同 | 相同 |

### 热部署流程对比

#### 之前的流程
```
文件修改 → 重新构建WAR → 删除旧WAR → 复制新WAR → Tomcat检测 → 解压WAR → 应用可用
                                                    ↑
                                              在这个过程中出现403/404
```

#### 现在的流程
```
静态文件修改 → 直接复制到exploded WAR目录 → 立即生效
Java文件修改 → 重新构建 → 重新部署exploded WAR → 立即生效
```

## 🔍 技术细节

### 1. 文件路径映射
```typescript
// Maven/Gradle项目路径映射
if (relativePath.startsWith("src/main/webapp/")) {
  // webapp资源直接映射到根目录
  relativePath = relativePath.substring("src/main/webapp/".length);
} else if (relativePath.startsWith("src/main/resources/")) {
  // resources资源映射到WEB-INF/classes
  relativePath = relativePath.substring("src/main/resources/".length);
  relativePath = path.join("WEB-INF/classes", relativePath);
}
```

### 2. 部署目录结构
```
webapps/
├── ROOT/                    # exploded WAR目录
│   ├── WEB-INF/
│   │   ├── web.xml
│   │   ├── classes/         # Java类和resources
│   │   └── lib/             # JAR依赖
│   ├── index.html           # webapp资源
│   ├── css/
│   └── js/
└── manager/                 # 其他应用
```

### 3. 热部署策略
- **静态文件**（HTML、CSS、JS、JSP）：直接复制，立即生效
- **Java文件**：触发完整重新部署（需要编译）
- **配置文件**：触发完整重新部署（需要重启应用）

## 🎯 用户体验改进

### 1. 部署稳定性
- ✅ **不再出现403/404**：exploded WAR部署避免了解压过程中的中断
- ✅ **立即生效**：文件修改后立即可以访问
- ✅ **与IDEA一致**：使用相同的部署方式

### 2. 热部署效率
- ✅ **真正的增量更新**：只更新修改的文件
- ✅ **快速响应**：静态文件修改几毫秒内生效
- ✅ **智能回退**：增量更新失败时自动回退到完整部署

### 3. 开发体验
- ✅ **所见即所得**：修改文件后立即看到效果
- ✅ **稳定可靠**：不会出现间歇性访问问题
- ✅ **调试友好**：详细的热部署日志

## 📋 修改的文件

1. **src/services/ProjectDeploymentService.ts**
   - 修改`deployWarFile`方法使用exploded WAR部署
   - 实现`extractWarFile`方法解压WAR文件
   - 移除应用可用性检测（不再需要）

2. **src/services/HotDeployService.ts**
   - 重新设计`performHotDeploy`方法支持真正的增量更新
   - 实现`updateStaticFilesToExplodedWar`方法直接操作exploded WAR
   - 实现`copyStaticFileToExplodedWar`方法进行文件替换

3. **package.json**
   - 添加`yauzl`依赖用于ZIP文件解压

## 🚀 测试验证

### 1. 部署测试
1. 部署项目后检查webapps目录是否有exploded WAR目录
2. 访问应用确认正常工作
3. 不应该出现403、404等错误

### 2. 热部署测试
1. 修改HTML、CSS、JS文件
2. 观察是否立即生效（几毫秒内）
3. 检查控制台是否有"✅ Hot deployed static file"日志

### 3. 稳定性测试
1. 连续修改多个文件
2. 访问应用确认始终可用
3. 不应该出现间歇性的访问问题

现在我们的热部署实现应该和IDEA的Tomcat插件一样稳定可靠了！
