# 🔧 重新部署重复应用修复

## 🐛 问题描述

用户反馈点击"重新部署"按钮后，在"Deployed Application"中出现重复的应用记录，而不是更新现有的记录。

## 🔍 问题分析

### 1. 根本原因
每次重新部署时，`WebViewManager`都会调用`ProjectConfiguration.createDefault()`创建新的项目配置，这会生成新的项目ID：

```javascript
// 问题代码
const projectConfig = ProjectConfiguration.createDefault(
  message.data.projectPath,
  message.data.projectName
);
```

```typescript
// ProjectConfiguration.createDefault方法
static createDefault(projectPath: string, projectName: string): ProjectConfiguration {
    const config: ProjectConfigurationData = {
        id: generateId(), // ❌ 每次都生成新的ID
        name: projectName,
        // ...
    };
}
```

### 2. 问题流程
1. **首次部署**：生成ID为`abc123`的项目配置
2. **应用记录**：在实例中保存ID为`abc123`的部署应用
3. **重新部署**：生成ID为`def456`的新项目配置
4. **重复记录**：由于ID不同，`addDeployedApp`认为是新应用，添加而不是更新

### 3. addDeployedApp逻辑
```typescript
addDeployedApp(app: DeployedApplication): void {
    const existingIndex = this.config.deployedApps.findIndex(a => a.id === app.id);
    if (existingIndex >= 0) {
        this.config.deployedApps[existingIndex] = app; // 更新现有
    } else {
        this.config.deployedApps.push(app); // ❌ 添加新记录
    }
}
```

由于ID不匹配，总是执行`push()`操作，导致重复记录。

## 🔧 解决方案

### 1. 添加createWithId方法

#### 在ProjectConfiguration中添加新方法
```typescript
/**
 * 使用指定ID创建配置
 */
static createWithId(
  id: string,
  projectPath: string,
  projectName: string
): ProjectConfiguration {
  const config: ProjectConfigurationData = {
    id: id, // ✅ 使用指定的ID
    name: projectName,
    projectPath,
    contextPath: "ROOT",
    build: {
      type: ProjectType.MAVEN,
      buildCommand: "mvn clean package",
      outputDirectory: "target",
      warFileName: `${projectName}.war`,
      autoBuild: true,
    },
    hotDeploy: {
      enabled: true,
      watchExtensions: ["java", "jsp", "html", "css", "js", "xml"],
      excludeDirectories: ["target", "node_modules", ".git"],
      deployDelay: 1000,
      restartApp: false,
    },
    environmentVariables: {},
    jvmArgs: [],
    createdAt: new Date(),
    lastModified: new Date(),
    enabled: true,
  };

  return new ProjectConfiguration(config);
}
```

### 2. 修改部署逻辑

#### 检查现有部署记录
```typescript
case "deployProject":
  try {
    // 检查是否已有相同项目的部署记录
    const { TomcatInstanceManager } = await import(
      "../services/TomcatInstanceManager"
    );
    const instanceManager = TomcatInstanceManager.getInstance();
    const instance = instanceManager.getInstance(message.data.instanceId);
    
    let projectConfig: any;
    let existingApp: any = null;
    
    if (instance) {
      // 查找是否已有相同项目路径的部署应用
      const deployedApps = instance.getDeployedApps();
      existingApp = deployedApps.find(app => 
        app.sourcePath === message.data.projectPath
      );
    }
    
    if (existingApp) {
      // ✅ 如果找到现有应用，使用相同的ID创建配置
      projectConfig = ProjectConfiguration.createWithId(
        existingApp.id,
        message.data.projectPath,
        message.data.projectName
      );
    } else {
      // 如果没有找到，创建新的配置
      projectConfig = ProjectConfiguration.createDefault(
        message.data.projectPath,
        message.data.projectName
      );
    }
    
    // 继续部署流程...
  }
```

### 3. 匹配逻辑

#### 使用项目路径匹配
使用`sourcePath`（项目源路径）来匹配现有的部署应用，而不是依赖项目名称：

```typescript
existingApp = deployedApps.find(app => 
  app.sourcePath === message.data.projectPath
);
```

这样可以确保即使项目名称略有不同，也能正确识别为同一个项目。

## ✅ 修复效果

### 修复前
- ❌ 重新部署时生成新的项目ID
- ❌ 在"Deployed Application"中出现重复记录
- ❌ 无法正确更新现有部署信息

### 修复后
- ✅ **ID复用**：重新部署时使用现有应用的ID
- ✅ **记录更新**：正确更新现有部署记录而不是添加新记录
- ✅ **一致性**：每个项目在实例中只有一条部署记录

## 🔄 部署流程

### 新的重新部署流程
1. **用户操作**：点击"重新部署"按钮
2. **确认对话框**：显示自定义确认对话框
3. **检查现有记录**：查找相同项目路径的部署应用
4. **ID处理**：
   - 如果找到现有应用：使用相同ID创建配置
   - 如果没有找到：生成新ID创建配置
5. **执行部署**：使用正确ID的配置进行部署
6. **更新记录**：`addDeployedApp`正确更新现有记录

### 匹配策略
- **主要匹配**：使用`sourcePath`（项目源路径）
- **精确匹配**：完整路径匹配，确保准确性
- **容错性**：即使项目名称变化也能正确匹配

## 📋 修改的文件

1. **src/models/ProjectConfiguration.ts**
   - 添加`createWithId()`静态方法
   - 支持使用指定ID创建项目配置

2. **src/webview/WebViewManager.ts**
   - 修改`deployProject`消息处理逻辑
   - 添加现有部署记录检查
   - 实现ID复用机制

3. **out/models/ProjectConfiguration.js**
   - 同步更新编译后的JavaScript文件

4. **out/webview/WebViewManager.js**
   - 同步更新编译后的JavaScript文件

## 🎯 用户体验

### 重新部署体验
1. **首次部署**：
   - 项目部署成功
   - 在"Deployed Application"中显示一条记录

2. **重新部署**：
   - 点击"重新部署"按钮
   - 确认后执行重新部署
   - 现有记录被更新，不会出现重复

3. **多次重新部署**：
   - 无论重新部署多少次
   - 始终只有一条部署记录
   - 记录信息（如部署时间）会正确更新

### 界面表现
- **部署列表**：每个项目只显示一条记录
- **状态更新**：部署时间、状态等信息正确更新
- **URL访问**：访问链接保持一致

## 🔍 测试验证

### 测试步骤
1. **部署项目**：首次部署一个项目
2. **检查记录**：确认"Deployed Application"中有一条记录
3. **重新部署**：点击"重新部署"按钮并确认
4. **验证结果**：
   - 确认只有一条部署记录
   - 确认部署时间已更新
   - 确认应用可以正常访问

### 预期结果
- ✅ 不会出现重复的部署记录
- ✅ 现有记录正确更新
- ✅ 应用功能正常

## 🛡️ 边界情况处理

### 1. 项目路径变化
如果项目被移动到不同路径，会被视为新项目，这是正确的行为。

### 2. 项目名称变化
如果只是项目名称变化但路径相同，仍会正确识别为同一项目。

### 3. 实例重启
实例重启后，部署记录会从配置文件中恢复，ID保持一致。

### 4. 并发部署
由于使用项目路径作为唯一标识，不会出现并发部署冲突。

现在重新部署功能应该不会再产生重复的应用记录了！
