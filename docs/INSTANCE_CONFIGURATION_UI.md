# ⚙️ 实例配置界面功能

## 🎯 用户需求

用户反馈添加Tomcat实例后，无法修改端口和JVM参数等配置。需要提供一个图形化界面来编辑已创建实例的配置。

## 🔧 解决方案

### 1. 创建实例配置WebView界面

#### HTML界面设计
```html
<div class="header">
    <h1>⚙️ 配置Tomcat实例</h1>
    <p>修改实例的端口、JVM参数、浏览器设置等配置</p>
</div>

<form id="config-form">
    <!-- 基本信息 -->
    <div class="card">
        <div class="card-header">基本信息</div>
        <div class="form-group">
            <label for="name">实例名称 *</label>
            <input type="text" id="name" name="name" required>
        </div>
        <div class="form-group">
            <label for="description">描述</label>
            <textarea id="description" name="description" rows="2"></textarea>
        </div>
    </div>
    
    <!-- 端口配置 -->
    <div class="card">
        <div class="card-header">端口配置</div>
        <div class="button-group">
            <button type="button" onclick="suggestPorts()">🎯 自动分配端口</button>
            <button type="button" onclick="validatePorts()">✅ 验证端口</button>
        </div>
        <!-- HTTP、HTTPS、AJP、JMX、Shutdown端口输入框 -->
    </div>
    
    <!-- JVM配置 -->
    <div class="card">
        <div class="card-header">JVM配置</div>
        <!-- JRE路径、堆内存、JVM参数配置 -->
    </div>
    
    <!-- 浏览器设置 -->
    <div class="card">
        <div class="card-header">浏览器设置</div>
        <!-- 浏览器类型、自动打开、默认页面配置 -->
    </div>
</form>
```

### 2. JavaScript功能实现

#### 配置加载和保存
```javascript
// 加载实例配置
function loadInstanceConfig(instance) {
    // 填充基本信息
    document.getElementById('name').value = instance.name || '';
    document.getElementById('description').value = instance.description || '';
    
    // 填充端口配置
    document.getElementById('httpPort').value = instance.ports.httpPort || '';
    document.getElementById('httpsPort').value = instance.ports.httpsPort || '';
    // ... 其他端口
    
    // 填充JVM配置
    document.getElementById('jrePath').value = instance.jvm.jrePath || '';
    document.getElementById('minHeapSize').value = instance.jvm.minHeapSize || '';
    document.getElementById('maxHeapSize').value = instance.jvm.maxHeapSize || '';
    
    // 填充JVM参数
    if (instance.jvm.additionalArgs && instance.jvm.additionalArgs.length > 0) {
        document.getElementById('jvmArgs').value = instance.jvm.additionalArgs.join('\\n');
    }
    
    // 填充浏览器配置
    document.getElementById('browserType').value = instance.browser.type || 'default';
    document.getElementById('autoOpenBrowser').checked = instance.browser.autoOpen !== false;
}

// 保存配置更新
window.updateInstance = function() {
    const formData = new FormData(document.getElementById('config-form'));
    
    // 处理JVM参数
    const jvmArgsText = formData.get('jvmArgs') || '';
    const jvmArgs = jvmArgsText
        .split('\\n')
        .map(arg => arg.trim())
        .filter(arg => arg.length > 0 && !arg.startsWith('#'));

    const updates = {
        name: formData.get('name'),
        description: formData.get('description'),
        ports: {
            httpPort: parseInt(formData.get('httpPort')),
            httpsPort: parseInt(formData.get('httpsPort')),
            ajpPort: parseInt(formData.get('ajpPort')),
            jmxPort: parseInt(formData.get('jmxPort')),
            shutdownPort: parseInt(formData.get('shutdownPort'))
        },
        jvm: {
            jrePath: formData.get('jrePath') || '',
            minHeapSize: formData.get('minHeapSize'),
            maxHeapSize: formData.get('maxHeapSize'),
            additionalArgs: jvmArgs
        },
        browser: {
            type: formData.get('browserType'),
            autoOpen: formData.has('autoOpenBrowser'),
            defaultPage: formData.get('defaultPage'),
            customPath: formData.get('customPath')
        }
    };
    
    showLoading();
    sendMessage('updateInstance', { instanceId: currentInstanceId, updates: updates });
};
```

### 3. 后端处理逻辑

#### WebView消息处理
```typescript
case "updateInstance":
  try {
    await instanceManager.updateInstance(
      message.data.instanceId,
      message.data.updates
    );
    panel.webview.postMessage({
      command: "instanceUpdated",
      data: { success: true },
    });
    vscode.window.showInformationMessage("实例配置更新成功！");
  } catch (error) {
    panel.webview.postMessage({
      command: "instanceUpdated",
      data: { success: false, error: String(error) },
    });
    vscode.window.showErrorMessage(`更新配置失败: ${error}`);
  }
  break;

case "loadInstance":
  try {
    const instance = instanceManager.getInstance(message.data.instanceId);
    if (instance) {
      panel.webview.postMessage({
        command: "instanceLoaded",
        data: { instance: instance.toJSON() },
      });
    }
  } catch (error) {
    console.error("Load instance error:", error);
  }
  break;
```

#### 实例配置更新
```typescript
async updateInstance(
  instanceId: string,
  updates: Partial<TomcatInstanceConfiguration>
): Promise<void> {
  const instance = this.instances.get(instanceId);
  if (!instance) {
    throw new Error(`Instance ${instanceId} not found`);
  }

  // 如果更新了端口配置，需要验证和重新预留端口
  if (updates.ports) {
    const errors = await this.portManager.validatePortConfiguration(
      updates.ports,
      instanceId
    );
    if (errors.length > 0) {
      throw new Error(`Port validation failed: ${errors.join(", ")}`);
    }

    // 释放旧端口，预留新端口
    this.portManager.releaseInstancePorts(instanceId);
    const portsReserved = await this.portManager.reserveInstancePorts(
      updates.ports,
      instanceId
    );
    if (!portsReserved) {
      throw new Error("Failed to reserve new ports");
    }
  }

  instance.updateConfiguration(updates);
  await this.saveInstances();

  // 如果实例正在运行且更新了关键配置，需要重启
  if (
    instance.getStatus() === TomcatInstanceStatus.RUNNING &&
    this.requiresRestart(updates)
  ) {
    await this.restartInstance(instanceId);
  }
}
```

### 4. 命令集成

#### 更新configureInstance命令
```typescript
// 配置实例
context.subscriptions.push(
  vscode.commands.registerCommand(
    "tomcatManager.configureInstance",
    async (item) => {
      const instanceId = getInstanceIdFromItem(item);
      if (!instanceId) return;

      try {
        const instance = instanceManager.getInstance(instanceId);
        if (!instance) return;

        // 使用WebView界面打开实例配置
        webViewManager.createOrShowPanel(
          WebViewType.INSTANCE_CONFIG,
          `配置实例: ${instance.getName()}`,
          { instanceId }
        );
      } catch (error) {
        statusBarManager.showErrorMessage(
          `Failed to open configuration: ${error}`
        );
      }
    }
  )
);
```

## ✅ 功能特性

### 1. 完整的配置编辑
- **基本信息**：实例名称、描述
- **端口配置**：HTTP、HTTPS、AJP、JMX、Shutdown端口
- **JVM配置**：JRE路径、堆内存、自定义JVM参数
- **浏览器设置**：浏览器类型、自动打开、默认页面

### 2. 智能功能
- **自动端口分配**：一键分配可用端口
- **端口验证**：检查端口冲突
- **JVM参数模板**：常用参数和Java 9+模块参数
- **文件夹选择**：JRE路径选择

### 3. 安全机制
- **端口冲突检测**：更新前验证端口可用性
- **自动重启**：关键配置更改后自动重启实例
- **配置验证**：确保配置的有效性

### 4. 用户体验
- **实时反馈**：配置更新状态提示
- **表单验证**：必填字段验证
- **加载状态**：显示保存进度
- **错误处理**：详细的错误信息

## 🔄 使用流程

### 1. 打开配置界面
1. 在Tomcat实例列表中右键点击实例
2. 选择"配置实例"菜单项
3. 系统打开配置WebView界面

### 2. 编辑配置
1. 界面自动加载当前实例配置
2. 修改需要更改的配置项
3. 使用快捷按钮辅助配置

### 3. 保存配置
1. 点击"💾 保存配置"按钮
2. 系统验证配置有效性
3. 更新实例配置并重启（如需要）

## 📋 修改的文件

1. **src/webview/HtmlTemplates.ts**
   - 添加`getInstanceConfigHtml()`方法
   - 创建完整的配置界面HTML
   - 实现配置加载和保存JavaScript逻辑

2. **src/webview/WebViewManager.ts**
   - 更新`getInstanceConfigHtml()`方法
   - 使用新的HTML模板

3. **src/extension.ts**
   - 更新`configureInstance`命令
   - 使用WebView界面替代对话框

4. **src/services/TomcatInstanceManager.ts**
   - 修复重复变量声明问题

## 🎯 用户体验

### 修改前
- ❌ 无法修改已创建实例的配置
- ❌ 只能通过删除重建来更改设置
- ❌ 缺少图形化配置界面

### 修改后
- ✅ **完整的配置编辑**：可修改所有实例配置
- ✅ **图形化界面**：直观的WebView配置界面
- ✅ **智能辅助**：端口分配、参数模板等功能
- ✅ **安全可靠**：配置验证和自动重启机制

现在用户可以方便地修改Tomcat实例的端口、JVM参数等所有配置了！
