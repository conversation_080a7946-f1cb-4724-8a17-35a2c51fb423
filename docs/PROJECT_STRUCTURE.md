# VSCode Tomcat Manager 项目结构

## 项目概览

这是一个完整的VSCode扩展项目，实现了类似IntelliJ IDEA的Tomcat热部署功能。项目采用TypeScript开发，支持多Tomcat实例管理、热部署、浏览器集成等功能。

## 目录结构

```
vscode-tomcat-manager/
├── .vscode/                    # VSCode配置
│   ├── launch.json            # 调试配置
│   └── tasks.json             # 任务配置
├── src/                       # 源代码目录
│   ├── extension.ts           # 扩展主入口文件
│   ├── models/                # 数据模型
│   │   ├── TomcatInstance.ts  # Tomcat实例模型
│   │   ├── ProjectConfiguration.ts # 项目配置模型
│   │   └── PortManager.ts     # 端口管理器
│   ├── services/              # 业务服务
│   │   ├── TomcatInstanceManager.ts # Tomcat实例管理服务
│   │   ├── ProjectDeploymentService.ts # 项目部署服务
│   │   ├── HotDeployService.ts # 热部署服务
│   │   ├── BrowserService.ts  # 浏览器服务
│   │   ├── PortDetectionService.ts # 端口检测服务
│   │   ├── ConfigurationManager.ts # 配置管理服务
│   │   └── ProjectManager.ts  # 项目管理服务
│   ├── views/                 # 用户界面
│   │   ├── TomcatExplorer.ts  # Tomcat资源管理器视图
│   │   └── StatusBarManager.ts # 状态栏管理器
│   └── test/                  # 测试文件
│       ├── runTest.ts         # 测试运行器
│       └── suite/             # 测试套件
│           ├── index.ts       # 测试入口
│           └── extension.test.ts # 扩展测试
├── out/                       # 编译输出目录
├── node_modules/              # 依赖包
├── package.json               # 项目配置和依赖
├── tsconfig.json              # TypeScript配置
├── .eslintrc.json             # ESLint配置
├── README.md                  # 项目说明文档
├── CHANGELOG.md               # 变更日志
├── USAGE.md                   # 使用指南
├── 需求文档.md                 # 需求文档
└── PROJECT_STRUCTURE.md       # 项目结构说明（本文件）
```

## 核心组件说明

### 1. 数据模型 (models/)

#### TomcatInstance.ts
- **TomcatInstance类**：Tomcat实例的核心数据模型
- **TomcatInstanceConfiguration接口**：实例配置结构
- **PortConfiguration接口**：端口配置
- **BrowserConfiguration接口**：浏览器配置
- **JvmConfiguration接口**：JVM配置
- **DeployedApplication接口**：部署应用信息

#### ProjectConfiguration.ts
- **ProjectConfiguration类**：项目配置管理
- **BuildConfiguration接口**：构建配置
- **HotDeployConfiguration接口**：热部署配置
- **ProjectType枚举**：项目类型（Maven、Gradle、Plain Java）

#### PortManager.ts
- **PortManager类**：端口管理单例
- **PortInfo接口**：端口信息
- **PortStatus枚举**：端口状态
- 端口预留、释放、冲突检测功能

### 2. 业务服务 (services/)

#### TomcatInstanceManager.ts
- Tomcat实例的生命周期管理
- 实例创建、删除、启动、停止、重启
- 实例配置更新
- 浏览器集成

#### ProjectDeploymentService.ts
- 项目部署功能
- 支持Maven、Gradle、Plain Java项目
- WAR文件构建和部署
- 项目类型检测和工作区扫描

#### HotDeployService.ts
- 文件监听和热部署
- 增量更新策略
- 可配置的文件扩展名和排除模式
- 智能部署延迟

#### BrowserService.ts
- 多浏览器支持
- 浏览器检测和验证
- 自定义浏览器路径支持
- 启动参数配置

#### PortDetectionService.ts
- 端口可用性检测
- 端口使用情况分析
- 端口冲突检测
- 端口使用报告生成

#### ConfigurationManager.ts
- 全局配置管理
- 实例和项目配置持久化
- 配置导入导出
- 配置备份和恢复

#### ProjectManager.ts
- 项目配置管理
- 项目搜索和过滤
- 批量操作支持
- 项目统计信息

### 3. 用户界面 (views/)

#### TomcatExplorer.ts
- **TomcatExplorerProvider类**：树形视图数据提供者
- **TomcatTreeItem类**：树节点项
- **TomcatExplorer类**：资源管理器主类
- 层级显示：实例 → 应用 → 配置信息

#### StatusBarManager.ts
- 状态栏项管理
- 实时状态更新
- 进度显示
- 快速操作按钮

### 4. 主入口 (extension.ts)

- 扩展激活和停用
- 命令注册和处理
- 服务初始化
- UI组件集成
- 事件处理和错误管理

## 技术架构

### 设计模式

1. **单例模式**：服务类使用单例模式确保全局唯一性
2. **观察者模式**：文件监听和状态变化通知
3. **工厂模式**：实例和项目配置创建
4. **策略模式**：不同项目类型的构建策略

### 数据流

```
用户操作 → 命令处理 → 服务调用 → 数据模型更新 → UI刷新
```

### 配置管理

- **全局配置**：VSCode设置系统
- **工作区配置**：`.vscode/tomcat-manager/`目录
- **实例配置**：`instances.json`文件
- **项目配置**：`projects.json`文件

### 错误处理

- 统一的错误处理机制
- 用户友好的错误消息
- 详细的调试日志
- 优雅的降级处理

## 开发指南

### 环境要求

- Node.js 16+
- VSCode 1.74.0+
- TypeScript 4.9+

### 开发命令

```bash
# 安装依赖
npm install

# 编译TypeScript
npm run compile

# 监听模式编译
npm run watch

# 代码检查
npm run lint

# 运行测试
npm test

# 打包扩展
npm run package
```

### 调试方法

1. 在VSCode中打开项目
2. 按F5启动扩展开发主机
3. 在新窗口中测试扩展功能
4. 使用开发者工具查看日志

### 测试策略

- **单元测试**：核心模型和服务的功能测试
- **集成测试**：组件间交互测试
- **端到端测试**：完整工作流程测试

## 扩展点

### 添加新功能

1. **新的项目类型支持**：
   - 在`ProjectType`枚举中添加新类型
   - 在`ProjectDeploymentService`中添加检测逻辑
   - 更新构建策略

2. **新的浏览器支持**：
   - 在`BrowserType`枚举中添加新类型
   - 在`BrowserService`中添加路径检测
   - 更新启动参数逻辑

3. **新的监控功能**：
   - 创建新的服务类
   - 在`extension.ts`中注册命令
   - 更新UI组件

### 配置扩展

- 在`package.json`的`contributes.configuration`中添加新配置项
- 在相应服务中读取和使用配置
- 更新文档说明

## 性能优化

### 已实现的优化

1. **懒加载**：服务按需初始化
2. **缓存机制**：端口状态和浏览器信息缓存
3. **防抖处理**：文件监听事件防抖
4. **异步操作**：所有IO操作异步化

### 进一步优化建议

1. **虚拟化**：大量实例时使用虚拟滚动
2. **增量更新**：只更新变化的UI部分
3. **后台任务**：长时间操作移到后台
4. **内存管理**：及时清理不用的资源

## 维护指南

### 版本发布

1. 更新`package.json`中的版本号
2. 更新`CHANGELOG.md`
3. 运行完整测试套件
4. 构建和打包扩展
5. 发布到VSCode市场

### 问题排查

1. 检查VSCode输出面板的日志
2. 启用调试模式获取详细信息
3. 检查配置文件完整性
4. 验证依赖环境

---

这个项目结构设计遵循了模块化、可扩展、可维护的原则，为VSCode Tomcat Manager提供了坚实的技术基础。
