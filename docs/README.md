# 📚 VSCode Tomcat Manager 文档

本目录包含VSCode Tomcat Manager扩展的完整文档。

## 📖 用户文档

### 基础使用
- [📋 使用指南](USAGE.md) - 基本功能和操作说明
- [🎯 图形化界面指南](GRAPHICAL_UI_GUIDE.md) - 详细的UI操作指南
- [🔧 增强版图形化界面指南](ENHANCED_UI_GUIDE.md) - 高级功能说明

### 项目管理
- [🔍 WAR项目检测指南](WAR_PROJECT_DETECTION_GUIDE.md) - 项目扫描和检测
- [📁 项目扫描改进](PROJECT_SCANNING_IMPROVEMENTS.md) - 扫描功能优化
- [🚀 自动项目加载](AUTO_PROJECT_LOADING.md) - 自动加载工作区项目

## 🔧 技术文档

### 功能实现
- [⚙️ JVM参数配置](JVM_ARGS_CONFIGURATION.md) - JVM参数设置功能
- [🚫 防止重复部署](PREVENT_DUPLICATE_DEPLOYMENT.md) - 重复部署防护机制
- [📺 Tomcat输出通道](TOMCAT_OUTPUT_CHANNEL.md) - 实时日志显示功能

### 问题修复
- [🔧 JAVA_HOME修复](JAVA_HOME_FIX.md) - Java环境变量问题解决
- [🔧 WAR文件路径修复](WAR_FILE_PATH_FIX.md) - 文件路径处理优化
- [🔧 Tomcat启动超时修复](TOMCAT_STARTUP_TIMEOUT_FIX.md) - 启动超时问题解决
- [🔧 JavaScript作用域修复](JAVASCRIPT_SCOPE_FIX.md) - WebView按钮响应问题

### 调试和诊断
- [🔍 文件夹选择调试](FOLDER_SELECTION_DEBUG.md) - 文件选择功能调试
- [🔍 WebView按钮调试](WEBVIEW_BUTTON_DEBUG.md) - 界面按钮问题诊断
- [🔧 Tomcat启动诊断](TOMCAT_STARTUP_DIAGNOSTICS.md) - 启动失败诊断功能

## 📋 项目信息

### 开发文档
- [🏗️ 项目结构](PROJECT_STRUCTURE.md) - 代码组织和架构说明
- [📝 更新日志](CHANGELOG.md) - 版本更新记录

## 🚀 快速开始

1. **安装扩展**：在VSCode扩展市场搜索"Tomcat Manager"
2. **阅读使用指南**：从[使用指南](USAGE.md)开始
3. **查看图形化界面**：参考[图形化界面指南](GRAPHICAL_UI_GUIDE.md)
4. **遇到问题**：查看相关的调试和修复文档

## 📞 支持

如果你遇到问题或有建议，请：
1. 首先查看相关的调试文档
2. 检查[更新日志](CHANGELOG.md)中是否有相关修复
3. 在GitHub仓库中提交Issue

## 🤝 贡献

欢迎贡献代码和文档！请参考：
- [项目结构](PROJECT_STRUCTURE.md)了解代码组织
- 查看现有文档的格式和风格
- 提交Pull Request前请确保文档更新

---

*最后更新：2025年7月*
