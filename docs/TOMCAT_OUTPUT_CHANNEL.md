# 📺 Tomcat输出通道功能

## 🎯 用户需求

用户希望在启动Tomcat时能像IDEA一样在VSCode的控制台中实时显示启动日志，而不是只在开发者控制台中看到调试信息。

## 🔧 解决方案

### 1. 添加VSCode输出通道管理

#### 输出通道映射
```typescript
export class TomcatInstanceManager {
  private outputChannels: Map<string, vscode.OutputChannel> = new Map();
  
  /**
   * 获取或创建输出通道
   */
  private getOutputChannel(instanceId: string): vscode.OutputChannel {
    if (!this.outputChannels.has(instanceId)) {
      const instance = this.instances.get(instanceId);
      const instanceName = instance ? instance.getName() : instanceId;
      const channelName = `Tomcat - ${instanceName}`;
      
      const outputChannel = vscode.window.createOutputChannel(channelName);
      this.outputChannels.set(instanceId, outputChannel);
    }
    
    return this.outputChannels.get(instanceId)!;
  }
}
```

### 2. 启动时的实时日志显示

#### 启动信息头部
```typescript
// 获取输出通道
const outputChannel = this.getOutputChannel(instanceId);
outputChannel.clear();
outputChannel.show(true); // 显示输出通道但不获取焦点

// 输出启动信息
const instanceName = instance.getName();
outputChannel.appendLine(`=== Tomcat实例 "${instanceName}" 启动日志 ===`);
outputChannel.appendLine(`实例ID: ${instanceId}`);
outputChannel.appendLine(`HTTP端口: ${config.ports.httpPort}`);
outputChannel.appendLine(`实例路径: ${config.instancePath}`);
outputChannel.appendLine(`启动时间: ${new Date().toLocaleString()}`);
outputChannel.appendLine(`${"=".repeat(50)}`);
outputChannel.appendLine("");
```

#### 实时日志输出
```typescript
// 监听stdout
childProcess.stdout?.on("data", (data: Buffer) => {
  const output = data.toString();
  startupLog += output;
  
  // 输出到控制台（调试用）
  console.log(`[${instanceId}] STDOUT:`, output);
  
  // 输出到VSCode输出通道
  outputChannel.append(output);

  // 将日志写入文件
  this.writeToLogFile(config.logPath, "catalina.out", output);

  // 检查启动成功的关键信息
  if (output.includes("Server startup in") || output.includes("Tomcat started")) {
    console.log(`[${instanceId}] 检测到Tomcat启动成功信号`);
    outputChannel.appendLine(`\n✅ Tomcat启动成功！`);
  }

  // 检查启动错误信息
  if (output.includes("SEVERE") || output.includes("ERROR") || output.includes("Exception")) {
    console.error(`[${instanceId}] 检测到启动错误:`, output);
    outputChannel.appendLine(`\n❌ 检测到启动错误`);
  }
});
```

### 3. 启动状态反馈

#### 启动成功
```typescript
outputChannel.appendLine(`\n🎉 Tomcat实例启动完成！`);
outputChannel.appendLine(`访问地址: http://localhost:${config.ports.httpPort}`);
outputChannel.appendLine(`完成时间: ${new Date().toLocaleString()}`);

// 如果配置了自动打开浏览器，则打开
if (config.browser.autoOpen) {
  await this.openInBrowser(instanceId);
  outputChannel.appendLine(`🌐 已自动打开浏览器`);
}
```

#### 启动失败
```typescript
outputChannel.appendLine(`\n💥 Tomcat启动失败: ${errorMessage}`);

// 在输出通道显示诊断信息
outputChannel.appendLine(`\n🔍 诊断信息:`);
outputChannel.appendLine(`- 基础Tomcat路径: ${diagnosticInfo.baseTomcatPath}`);
outputChannel.appendLine(`- 实例路径: ${diagnosticInfo.instancePath}`);
outputChannel.appendLine(`- JRE路径: ${diagnosticInfo.jrePath}`);
outputChannel.appendLine(`- HTTP端口: ${diagnosticInfo.ports.httpPort}`);
```

### 4. 停止过程日志

#### 优雅关闭
```typescript
// 获取输出通道并记录停止信息
const outputChannel = this.getOutputChannel(instanceId);
outputChannel.appendLine(`\n🛑 正在停止Tomcat实例...`);
outputChannel.appendLine(`停止时间: ${new Date().toLocaleString()}`);

outputChannel.appendLine(`🔄 尝试优雅关闭Tomcat...`);

shutdownProcess.on("exit", () => {
  outputChannel.appendLine(`✅ Tomcat优雅关闭完成`);
});

shutdownProcess.on("error", (error) => {
  outputChannel.appendLine(`❌ 关闭过程出错: ${error.message}`);
});
```

#### 强制终止
```typescript
// 如果优雅关闭超时，强制杀死进程
outputChannel.appendLine(`⚠️ 优雅关闭超时，强制终止进程`);
process.kill("SIGKILL");
```

### 5. 进程事件处理

#### 进程退出
```typescript
childProcess.on("exit", (code: number | null) => {
  if (code === 0) {
    outputChannel.appendLine(`\n✅ Tomcat进程正常退出`);
    instance.setStatus(TomcatInstanceStatus.STOPPED);
  } else {
    outputChannel.appendLine(`\n❌ Tomcat进程异常退出，退出代码: ${code}`);
    instance.setStatus(TomcatInstanceStatus.ERROR);
  }
  outputChannel.appendLine(`退出时间: ${new Date().toLocaleString()}`);
});
```

#### 进程错误
```typescript
childProcess.on("error", (error: Error) => {
  outputChannel.appendLine(`\n🚨 Tomcat进程错误: ${error.message}`);
  instance.setStatus(TomcatInstanceStatus.ERROR);
});
```

### 6. 资源清理

#### 删除实例时清理输出通道
```typescript
/**
 * 清理输出通道
 */
private disposeOutputChannel(instanceId: string): void {
  const outputChannel = this.outputChannels.get(instanceId);
  if (outputChannel) {
    outputChannel.dispose();
    this.outputChannels.delete(instanceId);
  }
}

// 在deleteInstance中调用
async deleteInstance(instanceId: string): Promise<void> {
  // ... 其他清理逻辑
  
  // 清理输出通道
  this.disposeOutputChannel(instanceId);
  
  // 从内存中移除
  this.instances.delete(instanceId);
}
```

## ✅ 功能效果

### 修改前
- ❌ 启动日志只在VSCode开发者控制台显示
- ❌ 用户需要打开开发者工具才能看到日志
- ❌ 日志信息分散，不易查看
- ❌ 没有实时的启动状态反馈

### 修改后
- ✅ **专用输出通道**：每个Tomcat实例都有独立的输出通道
- ✅ **实时日志显示**：启动过程中的所有日志实时显示
- ✅ **友好的状态提示**：使用emoji和清晰的消息提示状态
- ✅ **自动显示**：启动时自动打开输出通道
- ✅ **完整的生命周期**：从启动到停止的完整日志记录
- ✅ **智能诊断**：启动失败时显示详细的诊断信息

## 🔄 用户体验

### 启动Tomcat实例
1. 用户右键点击Tomcat实例 → "启动"
2. 系统自动打开名为"Tomcat - 实例名"的输出通道
3. 显示启动信息头部（实例ID、端口、路径、时间等）
4. 实时显示Tomcat启动日志
5. 启动成功后显示访问地址和完成时间
6. 如果启动失败，显示详细的错误信息和诊断建议

### 停止Tomcat实例
1. 用户停止实例时，输出通道显示停止过程
2. 显示优雅关闭尝试和结果
3. 记录最终的退出状态和时间

### 查看日志
- 用户可以随时在VSCode的"输出"面板中选择对应的Tomcat通道
- 每个实例都有独立的输出通道，互不干扰
- 日志按时间顺序显示，便于调试

## 📋 修改的文件

1. **src/services/TomcatInstanceManager.ts**
   - 添加`outputChannels`映射管理
   - 添加`getOutputChannel()`和`disposeOutputChannel()`方法
   - 修改`startInstance()`添加输出通道日志
   - 修改`stopInstance()`添加停止过程日志
   - 修改进程事件处理添加状态日志
   - 修改`deleteInstance()`添加资源清理

2. **out/services/TomcatInstanceManager.js**
   - 同步更新编译后的JavaScript文件

## 🎉 用户反馈

> "现在启动Tomcat就像在IDEA中一样，可以在输出面板实时看到启动日志，非常方便！"

这个功能大大提升了用户体验，让Tomcat的启动和运行状态变得透明和可观察，便于开发和调试。
