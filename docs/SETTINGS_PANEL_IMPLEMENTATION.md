# 🔧 设置面板功能实现

## 🎯 功能概述

实现了完整的Tomcat Manager设置面板，用户可以通过点击"Open Settings"按钮访问，配置扩展的全局设置和默认值。

## ✨ 主要功能

### 1. 基础设置
- **默认Tomcat路径**：设置新建实例时的默认Tomcat安装目录
- **默认JRE路径**：设置新建实例时的默认JRE/JDK路径
- 支持文件夹选择对话框

### 2. 端口范围设置
- **HTTP端口起始**：默认8080
- **HTTPS端口起始**：默认8443
- **AJP端口起始**：默认8009
- **JMX端口起始**：默认9999
- **Shutdown端口起始**：默认8005
- 网格布局，响应式设计

### 3. 浏览器设置
- **自动打开浏览器**：启动实例时是否自动打开浏览器
- **默认浏览器选择**：
  - 系统默认浏览器
  - Google Chrome
  - Mozilla Firefox
  - Safari
  - Microsoft Edge
  - 自定义浏览器（支持路径选择）

### 4. 设置管理
- **保存设置**：将配置保存到VSCode设置
- **恢复默认**：重置所有设置到默认值
- **导出设置**：将配置导出为JSON文件
- **导入设置**：从JSON文件导入配置

## 🎨 界面设计

### 1. 布局结构
```
设置面板
├── 基础设置
│   ├── 默认Tomcat路径 [输入框] [浏览按钮]
│   └── 默认JRE路径 [输入框] [浏览按钮]
├── 端口范围设置
│   ├── HTTP端口起始 [数字输入]
│   ├── HTTPS端口起始 [数字输入]
│   ├── AJP端口起始 [数字输入]
│   ├── JMX端口起始 [数字输入]
│   └── Shutdown端口起始 [数字输入]
├── 浏览器设置
│   ├── 自动打开浏览器 [复选框]
│   ├── 默认浏览器 [下拉选择]
│   └── 自定义浏览器路径 [输入框] [浏览按钮]
└── 操作按钮
    ├── 保存设置
    ├── 恢复默认
    ├── 导出设置
    └── 导入设置
```

### 2. 样式特性
- **VSCode主题适配**：使用VSCode的CSS变量
- **响应式设计**：端口设置使用网格布局
- **输入组合**：路径输入框配合浏览按钮
- **条件显示**：自定义浏览器路径根据选择显示/隐藏

## 🔧 技术实现

### 1. HTML模板
```typescript
// src/webview/HtmlTemplates.ts
static getSettingsPanelHtml(): string {
  const content = `
    <div class="header">
      <h1>⚙️ Tomcat Manager 设置</h1>
      <p>配置扩展的全局设置和默认值</p>
    </div>
    
    <form id="settingsForm">
      <!-- 基础设置 -->
      <div class="section">
        <h2>🏠 基础设置</h2>
        <!-- 表单内容 -->
      </div>
      
      <!-- 端口范围设置 -->
      <div class="section">
        <h2>🔌 端口范围设置</h2>
        <div class="port-grid">
          <!-- 端口输入框 -->
        </div>
      </div>
      
      <!-- 浏览器设置 -->
      <div class="section">
        <h2>🌐 浏览器设置</h2>
        <!-- 浏览器配置 -->
      </div>
    </form>
  `;
  
  return this.getBaseTemplate("Tomcat Manager 设置", content, scripts);
}
```

### 2. 消息处理
```typescript
// src/webview/WebViewManager.ts
private async handleSettingsMessage(
  message: WebViewMessage,
  panel: vscode.WebviewPanel
): Promise<void> {
  const configManager = ConfigurationManager.getInstance();

  switch (message.command) {
    case "loadSettings":
      // 加载当前设置
    case "updateSettings":
      // 保存设置更新
    case "selectBaseTomcatPath":
      // 选择Tomcat路径
    case "selectDefaultJrePath":
      // 选择JRE路径
    case "selectCustomBrowserPath":
      // 选择自定义浏览器路径
    case "exportSettings":
      // 导出设置到文件
    case "importSettings":
      // 从文件导入设置
  }
}
```

### 3. 前端交互
```javascript
// 页面初始化
function initPage() {
  // 浏览器选择变化处理
  document.getElementById('defaultBrowser').addEventListener('change', function() {
    const customPath = document.getElementById('customBrowserPath');
    if (this.value === 'custom') {
      customPath.style.display = 'block';
    } else {
      customPath.style.display = 'none';
    }
  });

  // 表单提交处理
  document.getElementById('settingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    saveSettings();
  });
}

// 设置保存
function saveSettings() {
  const formData = new FormData(document.getElementById('settingsForm'));
  const settings = {
    baseTomcatPath: formData.get('baseTomcatPath'),
    defaultJrePath: formData.get('defaultJrePath'),
    defaultBrowser: formData.get('defaultBrowser'),
    customBrowserPath: formData.get('customBrowserPath'),
    autoOpenBrowser: document.getElementById('autoOpenBrowser').checked,
    portRanges: {
      httpStart: parseInt(formData.get('httpStart')),
      httpsStart: parseInt(formData.get('httpsStart')),
      ajpStart: parseInt(formData.get('ajpStart')),
      jmxStart: parseInt(formData.get('jmxStart')),
      shutdownStart: parseInt(formData.get('shutdownStart'))
    }
  };

  sendMessage('updateSettings', settings);
}
```

## 📋 修改的文件

1. **src/webview/HtmlTemplates.ts**
   - 添加`getSettingsPanelHtml()`方法
   - 实现完整的设置面板HTML模板
   - 添加设置面板特有的CSS样式
   - 实现前端JavaScript交互逻辑

2. **src/webview/WebViewManager.ts**
   - 更新`getSettingsPanelHtml()`方法调用
   - 扩展`handleSettingsMessage()`消息处理
   - 添加文件选择对话框处理
   - 添加设置导入/导出功能

3. **src/extension.ts**
   - 已有`tomcatManager.openSettings`命令注册

4. **src/services/ConfigurationManager.ts**
   - 已有全局配置管理功能
   - 支持设置的加载、保存、导入、导出

## 🎯 用户体验

### 1. 设置访问
- 点击状态栏"Tomcat Manager"按钮
- 选择"Open Settings"
- 或使用命令面板：`Tomcat Manager: Open Settings`

### 2. 设置配置
- **直观界面**：分组清晰，功能明确
- **实时反馈**：操作成功/失败即时提示
- **路径选择**：点击浏览按钮选择文件夹/文件
- **表单验证**：端口范围验证（1024-65535）

### 3. 设置管理
- **一键恢复**：快速重置到默认值
- **配置备份**：导出设置到JSON文件
- **配置迁移**：从JSON文件导入设置
- **实时保存**：设置立即生效

## 🔍 设置项说明

### 1. 基础设置
- **默认Tomcat路径**：影响创建新实例时的默认路径
- **默认JRE路径**：影响创建新实例时的默认JRE

### 2. 端口范围
- **起始端口**：系统从这些端口开始寻找可用端口
- **自动分配**：创建实例时自动分配未占用的端口

### 3. 浏览器设置
- **自动打开**：启动实例时是否自动打开浏览器
- **浏览器选择**：指定使用哪个浏览器打开

## 🚀 扩展性

设置面板的架构支持轻松添加新的设置项：
- 在HTML模板中添加新的表单元素
- 在ConfigurationManager中添加新的配置字段
- 在消息处理中添加新的命令处理

现在设置面板功能完整，用户可以方便地配置Tomcat Manager的各项设置！
