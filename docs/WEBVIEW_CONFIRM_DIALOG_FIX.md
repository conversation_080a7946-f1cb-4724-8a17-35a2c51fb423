# 🔧 WebView确认对话框修复

## 🐛 问题描述

用户点击"重新部署"按钮时，控制台出现错误：
```
VM7:266 Ignored call to 'confirm()'. The document is sandboxed, and the 'allow-modals' keyword is not set.
```

## 🔍 问题分析

### 1. 根本原因
VSCode的WebView默认启用沙盒模式，不允许使用浏览器的原生模态对话框函数：
- `alert()`
- `confirm()`
- `prompt()`

### 2. 问题代码
```javascript
function redeploy() {
    if (!selectedProject) {
        showAlert('请先选择一个项目', 'error');
        return;
    }

    const result = confirm('确定要重新部署项目吗？这将覆盖之前的部署。'); // ❌ 被沙盒阻止
    if (!result) {
        return;
    }

    // 重新部署逻辑...
}
```

### 3. 沙盒限制
VSCode WebView的安全策略：
- 默认启用沙盒模式
- 阻止模态对话框以防止恶意脚本
- 需要显式设置`allow-modals`才能使用原生对话框

## 🔧 解决方案

### 1. 创建自定义确认对话框

#### 自定义showConfirm函数
```javascript
function showConfirm(message, onConfirm, onCancel) {
    // 创建确认对话框HTML
    const confirmHtml = `
        <div id="custom-confirm" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        ">
            <div style="
                background: var(--vscode-editor-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
                padding: 20px;
                min-width: 300px;
                max-width: 500px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            ">
                <div style="
                    margin-bottom: 20px;
                    color: var(--vscode-foreground);
                    font-size: 14px;
                    line-height: 1.5;
                ">\${message}</div>
                <div style="
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                ">
                    <button id="confirm-cancel" class="button button-secondary">取消</button>
                    <button id="confirm-ok" class="button button-primary">确定</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', confirmHtml);
    
    // 绑定事件
    document.getElementById('confirm-ok').onclick = function() {
        document.getElementById('custom-confirm').remove();
        if (onConfirm) onConfirm();
    };
    
    document.getElementById('confirm-cancel').onclick = function() {
        document.getElementById('custom-confirm').remove();
        if (onCancel) onCancel();
    };
    
    // 点击背景关闭
    document.getElementById('custom-confirm').onclick = function(e) {
        if (e.target.id === 'custom-confirm') {
            document.getElementById('custom-confirm').remove();
            if (onCancel) onCancel();
        }
    };
}
```

### 2. 修改redeploy函数

#### 使用自定义确认对话框
```javascript
function redeploy() {
    if (!selectedProject) {
        showAlert('请先选择一个项目', 'error');
        return;
    }

    // 使用自定义确认对话框
    showConfirm(
        '确定要重新部署项目吗？这将覆盖之前的部署。',
        function() {
            // 用户确认，执行重新部署
            // 重置部署状态
            isDeploying = false;
            isDeployed = false;

            // 清空之前的状态显示
            document.getElementById('deploy-status').innerHTML =
                '<div style="text-align: center; padding: 20px; color: var(--vscode-descriptionForeground);">准备重新部署...</div>';

            // 调用部署函数
            startDeploy();
        },
        function() {
            // 用户取消，不执行任何操作
            console.log('User cancelled redeploy');
        }
    );
}
```

## ✅ 修复效果

### 修复前
- ❌ 控制台错误：`Ignored call to 'confirm()'`
- ❌ 确认对话框无法显示
- ❌ 重新部署功能无法正常工作

### 修复后
- ✅ **自定义对话框**：完全兼容VSCode WebView环境
- ✅ **VSCode风格**：使用VSCode的颜色变量和样式
- ✅ **完整功能**：确认、取消、背景点击关闭
- ✅ **无错误**：不再有沙盒限制错误

## 🎨 界面特性

### 1. VSCode原生风格
- 使用VSCode的CSS变量
- 背景色：`var(--vscode-editor-background)`
- 边框色：`var(--vscode-panel-border)`
- 文字色：`var(--vscode-foreground)`

### 2. 交互体验
- **模态遮罩**：半透明黑色背景
- **居中显示**：对话框在屏幕中央
- **按钮样式**：使用现有的button样式类
- **键盘友好**：支持点击背景关闭

### 3. 响应式设计
- 最小宽度：300px
- 最大宽度：500px
- 自适应内容高度
- 阴影效果增强层次感

## 🔄 用户体验

### 重新部署流程
1. 用户点击"重新部署"按钮
2. 显示自定义确认对话框
3. 用户可以选择：
   - 点击"确定"：执行重新部署
   - 点击"取消"：取消操作
   - 点击背景：取消操作

### 对话框行为
- **非阻塞**：不会阻塞其他WebView功能
- **安全**：符合VSCode安全策略
- **一致性**：与VSCode界面风格保持一致

## 📋 修改的文件

1. **src/webview/HtmlTemplates.ts**
   - 添加`showConfirm()`自定义确认对话框函数
   - 修改`redeploy()`函数使用自定义对话框
   - 移除对原生`confirm()`的依赖

2. **out/webview/HtmlTemplates.js**
   - 同步更新编译后的JavaScript文件

## 🛡️ 安全考虑

### 1. 沙盒兼容
- 不依赖原生模态对话框
- 完全使用DOM操作实现
- 符合VSCode WebView安全策略

### 2. 内存管理
- 对话框使用后自动清理
- 事件监听器正确移除
- 避免内存泄漏

### 3. 用户体验
- 提供明确的确认和取消选项
- 支持多种关闭方式
- 防止意外操作

## 🔮 扩展性

这个自定义确认对话框可以在其他需要确认的场景中复用：
- 删除实例确认
- 停止服务确认
- 重置配置确认
- 清空日志确认

## 🎯 测试步骤

1. **部署项目**：完成一次项目部署
2. **点击重新部署**：点击"重新部署"按钮
3. **验证对话框**：
   - 确认对话框正常显示
   - 样式符合VSCode风格
   - 按钮功能正常
4. **测试交互**：
   - 点击"确定"应该执行重新部署
   - 点击"取消"应该关闭对话框
   - 点击背景应该关闭对话框
5. **检查控制台**：不应该有任何错误信息

现在重新部署功能应该能够正常工作，不再有沙盒限制错误！
