import * as vscode from 'vscode';
import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { BrowserType } from '../models/TomcatInstance';

/**
 * 浏览器信息接口
 */
export interface BrowserInfo {
    type: BrowserType;
    name: string;
    path: string;
    isAvailable: boolean;
    version?: string;
}

/**
 * 浏览器启动选项
 */
export interface BrowserLaunchOptions {
    url: string;
    browserType: BrowserType;
    customPath?: string;
    incognito?: boolean;
    newWindow?: boolean;
    additionalArgs?: string[];
}

/**
 * 浏览器服务
 */
export class BrowserService {
    private static instance: BrowserService;
    private availableBrowsers: Map<BrowserType, BrowserInfo> = new Map();
    private browserPaths: Map<BrowserType, string[]> = new Map();

    private constructor() {
        this.initializeBrowserPaths();
        this.detectAvailableBrowsers();
    }

    /**
     * 获取单例实例
     */
    static getInstance(): BrowserService {
        if (!BrowserService.instance) {
            BrowserService.instance = new BrowserService();
        }
        return BrowserService.instance;
    }

    /**
     * 在浏览器中打开URL
     */
    async openUrl(options: BrowserLaunchOptions): Promise<void> {
        const { url, browserType, customPath, incognito, newWindow, additionalArgs } = options;

        try {
            const command = await this.getBrowserCommand(browserType, customPath);
            const args = this.buildBrowserArgs(browserType, url, incognito, newWindow, additionalArgs);

            console.log(`Opening URL in browser: ${command} ${args.join(' ')}`);

            const process = spawn(command, args, {
                detached: true,
                stdio: 'ignore'
            });

            process.unref();

        } catch (error) {
            console.error('Failed to open URL in browser:', error);
            throw new Error(`Failed to open URL in browser: ${error}`);
        }
    }

    /**
     * 获取可用的浏览器列表
     */
    getAvailableBrowsers(): BrowserInfo[] {
        return Array.from(this.availableBrowsers.values());
    }

    /**
     * 检查浏览器是否可用
     */
    isBrowserAvailable(browserType: BrowserType): boolean {
        const browser = this.availableBrowsers.get(browserType);
        return browser ? browser.isAvailable : false;
    }

    /**
     * 获取浏览器信息
     */
    getBrowserInfo(browserType: BrowserType): BrowserInfo | undefined {
        return this.availableBrowsers.get(browserType);
    }

    /**
     * 刷新浏览器检测
     */
    async refreshBrowserDetection(): Promise<void> {
        this.availableBrowsers.clear();
        await this.detectAvailableBrowsers();
    }

    /**
     * 获取默认浏览器
     */
    getDefaultBrowser(): BrowserInfo | undefined {
        // 优先返回系统默认浏览器
        const defaultBrowser = this.availableBrowsers.get(BrowserType.DEFAULT);
        if (defaultBrowser?.isAvailable) {
            return defaultBrowser;
        }

        // 如果没有系统默认浏览器，按优先级返回可用浏览器
        const priorities = [BrowserType.CHROME, BrowserType.FIREFOX, BrowserType.EDGE, BrowserType.SAFARI];
        
        for (const type of priorities) {
            const browser = this.availableBrowsers.get(type);
            if (browser?.isAvailable) {
                return browser;
            }
        }

        return undefined;
    }

    /**
     * 验证自定义浏览器路径
     */
    validateCustomBrowserPath(browserPath: string): boolean {
        if (!browserPath || !fs.existsSync(browserPath)) {
            return false;
        }

        try {
            const stats = fs.statSync(browserPath);
            return stats.isFile();
        } catch (error) {
            return false;
        }
    }

    /**
     * 初始化浏览器路径
     */
    private initializeBrowserPaths(): void {
        const platform = process.platform;

        if (platform === 'win32') {
            this.browserPaths.set(BrowserType.CHROME, [
                'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
                'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
                path.join(process.env.LOCALAPPDATA || '', 'Google\\Chrome\\Application\\chrome.exe')
            ]);

            this.browserPaths.set(BrowserType.FIREFOX, [
                'C:\\Program Files\\Mozilla Firefox\\firefox.exe',
                'C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe'
            ]);

            this.browserPaths.set(BrowserType.EDGE, [
                'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
                'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe'
            ]);

        } else if (platform === 'darwin') {
            this.browserPaths.set(BrowserType.CHROME, [
                '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
            ]);

            this.browserPaths.set(BrowserType.FIREFOX, [
                '/Applications/Firefox.app/Contents/MacOS/firefox'
            ]);

            this.browserPaths.set(BrowserType.SAFARI, [
                '/Applications/Safari.app/Contents/MacOS/Safari'
            ]);

            this.browserPaths.set(BrowserType.EDGE, [
                '/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge'
            ]);

        } else {
            // Linux
            this.browserPaths.set(BrowserType.CHROME, [
                '/usr/bin/google-chrome',
                '/usr/bin/google-chrome-stable',
                '/usr/bin/chromium-browser',
                '/usr/bin/chromium'
            ]);

            this.browserPaths.set(BrowserType.FIREFOX, [
                '/usr/bin/firefox'
            ]);

            this.browserPaths.set(BrowserType.EDGE, [
                '/usr/bin/microsoft-edge',
                '/usr/bin/microsoft-edge-stable'
            ]);
        }
    }

    /**
     * 检测可用的浏览器
     */
    private async detectAvailableBrowsers(): Promise<void> {
        // 检测系统默认浏览器
        this.availableBrowsers.set(BrowserType.DEFAULT, {
            type: BrowserType.DEFAULT,
            name: 'System Default',
            path: 'default',
            isAvailable: true
        });

        // 检测其他浏览器
        for (const [type, paths] of this.browserPaths.entries()) {
            const browserInfo = await this.detectBrowser(type, paths);
            if (browserInfo) {
                this.availableBrowsers.set(type, browserInfo);
            }
        }
    }

    /**
     * 检测单个浏览器
     */
    private async detectBrowser(type: BrowserType, paths: string[]): Promise<BrowserInfo | null> {
        for (const browserPath of paths) {
            if (fs.existsSync(browserPath)) {
                try {
                    const version = await this.getBrowserVersion(type, browserPath);
                    return {
                        type,
                        name: this.getBrowserName(type),
                        path: browserPath,
                        isAvailable: true,
                        version
                    };
                } catch (error) {
                    // 继续尝试下一个路径
                }
            }
        }

        return null;
    }

    /**
     * 获取浏览器名称
     */
    private getBrowserName(type: BrowserType): string {
        switch (type) {
            case BrowserType.CHROME:
                return 'Google Chrome';
            case BrowserType.FIREFOX:
                return 'Mozilla Firefox';
            case BrowserType.SAFARI:
                return 'Safari';
            case BrowserType.EDGE:
                return 'Microsoft Edge';
            case BrowserType.DEFAULT:
                return 'System Default';
            case BrowserType.CUSTOM:
                return 'Custom Browser';
            default:
                return 'Unknown';
        }
    }

    /**
     * 获取浏览器版本
     */
    private async getBrowserVersion(type: BrowserType, browserPath: string): Promise<string | undefined> {
        try {
            let versionArg: string;
            
            switch (type) {
                case BrowserType.CHROME:
                    versionArg = '--version';
                    break;
                case BrowserType.FIREFOX:
                    versionArg = '--version';
                    break;
                case BrowserType.EDGE:
                    versionArg = '--version';
                    break;
                default:
                    return undefined;
            }

            return new Promise((resolve) => {
                const process = spawn(browserPath, [versionArg], { stdio: 'pipe' });
                let output = '';

                process.stdout?.on('data', (data) => {
                    output += data.toString();
                });

                process.on('exit', () => {
                    const version = output.trim().split(' ').pop();
                    resolve(version);
                });

                process.on('error', () => {
                    resolve(undefined);
                });

                // 超时处理
                setTimeout(() => {
                    process.kill();
                    resolve(undefined);
                }, 5000);
            });

        } catch (error) {
            return undefined;
        }
    }

    /**
     * 获取浏览器启动命令
     */
    private async getBrowserCommand(type: BrowserType, customPath?: string): Promise<string> {
        if (type === BrowserType.CUSTOM) {
            if (!customPath || !this.validateCustomBrowserPath(customPath)) {
                throw new Error('Invalid custom browser path');
            }
            return customPath;
        }

        if (type === BrowserType.DEFAULT) {
            return this.getSystemDefaultBrowserCommand();
        }

        const browser = this.availableBrowsers.get(type);
        if (!browser || !browser.isAvailable) {
            throw new Error(`Browser ${type} is not available`);
        }

        return browser.path;
    }

    /**
     * 获取系统默认浏览器命令
     */
    private getSystemDefaultBrowserCommand(): string {
        const platform = process.platform;
        
        if (platform === 'win32') {
            return 'start';
        } else if (platform === 'darwin') {
            return 'open';
        } else {
            return 'xdg-open';
        }
    }

    /**
     * 构建浏览器启动参数
     */
    private buildBrowserArgs(
        type: BrowserType, 
        url: string, 
        incognito?: boolean, 
        newWindow?: boolean,
        additionalArgs?: string[]
    ): string[] {
        const args: string[] = [];

        if (type === BrowserType.DEFAULT) {
            if (process.platform === 'win32') {
                args.push('', url); // start命令需要空的第一个参数
            } else {
                args.push(url);
            }
            return args;
        }

        // 添加浏览器特定参数
        switch (type) {
            case BrowserType.CHROME:
                if (incognito) {
                    args.push('--incognito');
                }
                if (newWindow) {
                    args.push('--new-window');
                }
                break;

            case BrowserType.FIREFOX:
                if (incognito) {
                    args.push('--private-window');
                }
                if (newWindow) {
                    args.push('--new-window');
                }
                break;

            case BrowserType.EDGE:
                if (incognito) {
                    args.push('--inprivate');
                }
                if (newWindow) {
                    args.push('--new-window');
                }
                break;

            case BrowserType.SAFARI:
                // Safari参数相对简单
                break;
        }

        // 添加自定义参数
        if (additionalArgs) {
            args.push(...additionalArgs);
        }

        // 添加URL
        args.push(url);

        return args;
    }
}
