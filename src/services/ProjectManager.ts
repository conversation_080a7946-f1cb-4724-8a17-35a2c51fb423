import * as vscode from 'vscode';
import { ProjectConfiguration, ProjectConfigurationData } from '../models/ProjectConfiguration';
import { ConfigurationManager } from './ConfigurationManager';

/**
 * 项目管理器
 */
export class ProjectManager {
    private static instance: ProjectManager;
    private projects: Map<string, ProjectConfiguration> = new Map();
    private configManager: ConfigurationManager;

    private constructor() {
        this.configManager = ConfigurationManager.getInstance();
        this.loadProjects();
    }

    /**
     * 获取单例实例
     */
    static getInstance(): ProjectManager {
        if (!ProjectManager.instance) {
            ProjectManager.instance = new ProjectManager();
        }
        return ProjectManager.instance;
    }

    /**
     * 添加项目
     */
    async addProject(project: ProjectConfiguration): Promise<void> {
        this.projects.set(project.getId(), project);
        await this.saveProjects();
    }

    /**
     * 删除项目
     */
    async removeProject(projectId: string): Promise<void> {
        this.projects.delete(projectId);
        await this.saveProjects();
    }

    /**
     * 更新项目
     */
    async updateProject(projectId: string, updates: Partial<ProjectConfigurationData>): Promise<void> {
        const project = this.projects.get(projectId);
        if (!project) {
            throw new Error(`Project ${projectId} not found`);
        }

        project.updateConfiguration(updates);
        await this.saveProjects();
    }

    /**
     * 获取项目
     */
    getProject(projectId: string): ProjectConfiguration | undefined {
        return this.projects.get(projectId);
    }

    /**
     * 获取所有项目
     */
    getAllProjects(): ProjectConfiguration[] {
        return Array.from(this.projects.values());
    }

    /**
     * 根据路径查找项目
     */
    findProjectByPath(projectPath: string): ProjectConfiguration | undefined {
        for (const project of this.projects.values()) {
            if (project.getProjectPath() === projectPath) {
                return project;
            }
        }
        return undefined;
    }

    /**
     * 根据Tomcat实例ID获取项目
     */
    getProjectsByInstanceId(instanceId: string): ProjectConfiguration[] {
        return Array.from(this.projects.values()).filter(project => 
            project.getTomcatInstanceId() === instanceId
        );
    }

    /**
     * 获取启用热部署的项目
     */
    getHotDeployEnabledProjects(): ProjectConfiguration[] {
        return Array.from(this.projects.values()).filter(project => 
            project.isHotDeployEnabled()
        );
    }

    /**
     * 验证项目配置
     */
    validateProject(project: ProjectConfiguration): string[] {
        return project.validateConfiguration();
    }

    /**
     * 导入项目配置
     */
    async importProjects(projects: ProjectConfigurationData[]): Promise<void> {
        for (const projectData of projects) {
            const project = ProjectConfiguration.fromJSON(projectData);
            this.projects.set(project.getId(), project);
        }
        await this.saveProjects();
    }

    /**
     * 导出项目配置
     */
    exportProjects(): ProjectConfigurationData[] {
        return Array.from(this.projects.values()).map(project => project.toJSON());
    }

    /**
     * 清理无效项目
     */
    async cleanupInvalidProjects(): Promise<string[]> {
        const removedProjects: string[] = [];
        
        for (const [projectId, project] of this.projects.entries()) {
            const errors = this.validateProject(project);
            if (errors.length > 0) {
                // 检查项目路径是否存在
                const projectPath = project.getProjectPath();
                try {
                    await vscode.workspace.fs.stat(vscode.Uri.file(projectPath));
                } catch (error) {
                    // 项目路径不存在，移除项目
                    this.projects.delete(projectId);
                    removedProjects.push(project.getName());
                }
            }
        }

        if (removedProjects.length > 0) {
            await this.saveProjects();
        }

        return removedProjects;
    }

    /**
     * 重置所有项目配置
     */
    async resetAllProjects(): Promise<void> {
        this.projects.clear();
        await this.saveProjects();
    }

    /**
     * 获取项目统计信息
     */
    getProjectStatistics(): {
        totalProjects: number;
        enabledProjects: number;
        hotDeployProjects: number;
        deployedProjects: number;
        projectsByType: { [key: string]: number };
    } {
        const projects = Array.from(this.projects.values());
        const totalProjects = projects.length;
        const enabledProjects = projects.filter(p => p.getConfiguration().enabled).length;
        const hotDeployProjects = projects.filter(p => p.isHotDeployEnabled()).length;
        const deployedProjects = projects.filter(p => p.getTomcatInstanceId()).length;

        const projectsByType: { [key: string]: number } = {};
        for (const project of projects) {
            const type = project.getConfiguration().build.type;
            projectsByType[type] = (projectsByType[type] || 0) + 1;
        }

        return {
            totalProjects,
            enabledProjects,
            hotDeployProjects,
            deployedProjects,
            projectsByType
        };
    }

    /**
     * 搜索项目
     */
    searchProjects(query: string): ProjectConfiguration[] {
        const lowerQuery = query.toLowerCase();
        return Array.from(this.projects.values()).filter(project => {
            const config = project.getConfiguration();
            return config.name.toLowerCase().includes(lowerQuery) ||
                   config.projectPath.toLowerCase().includes(lowerQuery) ||
                   config.contextPath.toLowerCase().includes(lowerQuery);
        });
    }

    /**
     * 克隆项目配置
     */
    cloneProject(sourceProjectId: string, newName: string, newPath: string): ProjectConfiguration {
        const sourceProject = this.projects.get(sourceProjectId);
        if (!sourceProject) {
            throw new Error(`Source project ${sourceProjectId} not found`);
        }

        const sourceConfig = sourceProject.getConfiguration();
        const newConfig: ProjectConfigurationData = {
            ...sourceConfig,
            id: this.generateProjectId(),
            name: newName,
            projectPath: newPath,
            tomcatInstanceId: undefined, // 新项目不关联实例
            createdAt: new Date(),
            lastModified: new Date(),
            lastDeployed: undefined
        };

        return ProjectConfiguration.fromJSON(newConfig);
    }

    /**
     * 批量更新项目
     */
    async batchUpdateProjects(
        projectIds: string[], 
        updates: Partial<ProjectConfigurationData>
    ): Promise<void> {
        for (const projectId of projectIds) {
            const project = this.projects.get(projectId);
            if (project) {
                project.updateConfiguration(updates);
            }
        }
        await this.saveProjects();
    }

    /**
     * 获取项目依赖关系
     */
    getProjectDependencies(): { [projectId: string]: string[] } {
        const dependencies: { [projectId: string]: string[] } = {};
        
        // 这里可以实现项目间依赖关系的分析
        // 例如通过分析pom.xml或build.gradle文件
        
        return dependencies;
    }

    /**
     * 加载项目
     */
    private async loadProjects(): Promise<void> {
        try {
            const projectsData = await this.configManager.loadProjects();
            this.projects.clear();
            
            for (const projectData of projectsData) {
                const project = ProjectConfiguration.fromJSON(projectData);
                this.projects.set(project.getId(), project);
            }
        } catch (error) {
            console.error('Failed to load projects:', error);
        }
    }

    /**
     * 保存项目
     */
    private async saveProjects(): Promise<void> {
        try {
            const projectsData = Array.from(this.projects.values()).map(project => project.toJSON());
            await this.configManager.saveProjects(projectsData);
        } catch (error) {
            console.error('Failed to save projects:', error);
            throw error;
        }
    }

    /**
     * 生成项目ID
     */
    private generateProjectId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 监听工作区变化
     */
    setupWorkspaceWatcher(): vscode.Disposable {
        const watcher = vscode.workspace.onDidChangeWorkspaceFolders(async (event) => {
            // 当工作区文件夹变化时，清理无效项目
            const removedProjects = await this.cleanupInvalidProjects();
            if (removedProjects.length > 0) {
                vscode.window.showInformationMessage(
                    `Removed ${removedProjects.length} invalid project(s): ${removedProjects.join(', ')}`
                );
            }
        });

        return watcher;
    }

    /**
     * 获取项目配置模板
     */
    getProjectTemplates(): { [templateName: string]: Partial<ProjectConfigurationData> } {
        return {
            'Maven Web Project': {
                build: {
                    type: 'maven' as any,
                    buildCommand: 'mvn clean package',
                    outputDirectory: 'target',
                    warFileName: '${project.name}.war',
                    autoBuild: true
                },
                hotDeploy: {
                    enabled: true,
                    watchExtensions: ['java', 'jsp', 'html', 'css', 'js', 'xml'],
                    excludeDirectories: ['target', 'node_modules', '.git'],
                    deployDelay: 1000,
                    restartApp: false
                }
            },
            'Gradle Web Project': {
                build: {
                    type: 'gradle' as any,
                    buildCommand: './gradlew build',
                    outputDirectory: 'build/libs',
                    warFileName: '${project.name}.war',
                    autoBuild: true
                },
                hotDeploy: {
                    enabled: true,
                    watchExtensions: ['java', 'jsp', 'html', 'css', 'js', 'xml'],
                    excludeDirectories: ['build', 'node_modules', '.git'],
                    deployDelay: 1000,
                    restartApp: false
                }
            },
            'Plain Java Web Project': {
                build: {
                    type: 'plain-java' as any,
                    buildCommand: 'ant war',
                    outputDirectory: 'dist',
                    warFileName: '${project.name}.war',
                    autoBuild: false
                },
                hotDeploy: {
                    enabled: false,
                    watchExtensions: ['java', 'jsp', 'html', 'css', 'js'],
                    excludeDirectories: ['build', '.git'],
                    deployDelay: 2000,
                    restartApp: true
                }
            }
        };
    }
}
