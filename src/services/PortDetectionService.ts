import * as net from 'net';
import { exec } from 'child_process';
import { promisify } from 'util';
import { PortManager, PortInfo, PortStatus } from '../models/PortManager';

const execAsync = promisify(exec);

/**
 * 端口使用信息
 */
export interface PortUsageInfo {
    port: number;
    protocol: 'tcp' | 'udp';
    state: 'LISTEN' | 'ESTABLISHED' | 'TIME_WAIT' | 'CLOSE_WAIT' | string;
    processId?: number;
    processName?: string;
    address: string;
}

/**
 * 端口冲突信息
 */
export interface PortConflictInfo {
    port: number;
    conflictType: 'system_process' | 'tomcat_instance' | 'reserved';
    conflictWith: string;
    recommendation: string;
}

/**
 * 端口检测服务
 */
export class PortDetectionService {
    private static instance: PortDetectionService;
    private portManager: PortManager;

    private constructor() {
        this.portManager = PortManager.getInstance();
    }

    /**
     * 获取单例实例
     */
    static getInstance(): PortDetectionService {
        if (!PortDetectionService.instance) {
            PortDetectionService.instance = new PortDetectionService();
        }
        return PortDetectionService.instance;
    }

    /**
     * 检测端口是否可用
     */
    async isPortAvailable(port: number, host: string = 'localhost'): Promise<boolean> {
        return new Promise((resolve) => {
            const server = net.createServer();
            
            server.listen(port, host, () => {
                server.once('close', () => {
                    resolve(true);
                });
                server.close();
            });
            
            server.on('error', () => {
                resolve(false);
            });
        });
    }

    /**
     * 获取端口使用信息
     */
    async getPortUsageInfo(port: number): Promise<PortUsageInfo | null> {
        try {
            if (process.platform === 'win32') {
                return await this.getPortUsageInfoWindows(port);
            } else {
                return await this.getPortUsageInfoUnix(port);
            }
        } catch (error) {
            console.error(`Failed to get port usage info for port ${port}:`, error);
            return null;
        }
    }

    /**
     * 获取Windows系统端口使用信息
     */
    private async getPortUsageInfoWindows(port: number): Promise<PortUsageInfo | null> {
        try {
            const { stdout } = await execAsync(`netstat -ano | findstr :${port}`);
            const lines = stdout.trim().split('\n');
            
            for (const line of lines) {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 5) {
                    const localAddress = parts[1];
                    const state = parts[3];
                    const processId = parseInt(parts[4]);
                    
                    if (localAddress.endsWith(`:${port}`)) {
                        // 获取进程名称
                        let processName = 'Unknown';
                        try {
                            const { stdout: processInfo } = await execAsync(`tasklist /FI "PID eq ${processId}" /FO CSV /NH`);
                            const processLine = processInfo.trim().split('\n')[0];
                            if (processLine) {
                                processName = processLine.split(',')[0].replace(/"/g, '');
                            }
                        } catch (error) {
                            // 忽略获取进程名称的错误
                        }

                        return {
                            port,
                            protocol: 'tcp',
                            state,
                            processId,
                            processName,
                            address: localAddress
                        };
                    }
                }
            }
        } catch (error) {
            // 如果netstat命令失败，返回null
        }
        
        return null;
    }

    /**
     * 获取Unix系统端口使用信息
     */
    private async getPortUsageInfoUnix(port: number): Promise<PortUsageInfo | null> {
        try {
            // 尝试使用lsof命令
            const { stdout } = await execAsync(`lsof -i :${port} -P -n`);
            const lines = stdout.trim().split('\n');
            
            if (lines.length > 1) {
                const dataLine = lines[1]; // 跳过标题行
                const parts = dataLine.trim().split(/\s+/);
                
                if (parts.length >= 9) {
                    const processName = parts[0];
                    const processId = parseInt(parts[1]);
                    const address = parts[8];
                    
                    return {
                        port,
                        protocol: 'tcp',
                        state: 'LISTEN',
                        processId,
                        processName,
                        address
                    };
                }
            }
        } catch (error) {
            // 如果lsof命令失败，尝试使用netstat
            try {
                const { stdout } = await execAsync(`netstat -tlnp | grep :${port}`);
                const lines = stdout.trim().split('\n');
                
                for (const line of lines) {
                    const parts = line.trim().split(/\s+/);
                    if (parts.length >= 7) {
                        const address = parts[3];
                        const state = parts[5];
                        const processInfo = parts[6];
                        
                        if (address.endsWith(`:${port}`)) {
                            let processId: number | undefined;
                            let processName = 'Unknown';
                            
                            if (processInfo && processInfo !== '-') {
                                const match = processInfo.match(/(\d+)\/(.+)/);
                                if (match) {
                                    processId = parseInt(match[1]);
                                    processName = match[2];
                                }
                            }
                            
                            return {
                                port,
                                protocol: 'tcp',
                                state,
                                processId,
                                processName,
                                address
                            };
                        }
                    }
                }
            } catch (netstatError) {
                // 忽略netstat错误
            }
        }
        
        return null;
    }

    /**
     * 检测端口范围内的可用端口
     */
    async findAvailablePortsInRange(startPort: number, endPort: number, count: number = 1): Promise<number[]> {
        const availablePorts: number[] = [];
        
        for (let port = startPort; port <= endPort && availablePorts.length < count; port++) {
            const isAvailable = await this.isPortAvailable(port);
            if (isAvailable) {
                availablePorts.push(port);
            }
        }
        
        return availablePorts;
    }

    /**
     * 检测端口冲突
     */
    async detectPortConflicts(ports: number[]): Promise<PortConflictInfo[]> {
        const conflicts: PortConflictInfo[] = [];
        
        for (const port of ports) {
            const portInfo = await this.portManager.getPortStatus(port);
            
            if (portInfo.status === PortStatus.IN_USE) {
                const usageInfo = await this.getPortUsageInfo(port);
                
                conflicts.push({
                    port,
                    conflictType: 'system_process',
                    conflictWith: usageInfo ? `${usageInfo.processName} (PID: ${usageInfo.processId})` : 'Unknown process',
                    recommendation: `Port ${port} is in use. Please choose a different port or stop the conflicting process.`
                });
            } else if (portInfo.status === PortStatus.RESERVED) {
                conflicts.push({
                    port,
                    conflictType: 'tomcat_instance',
                    conflictWith: portInfo.usedBy || 'Unknown instance',
                    recommendation: `Port ${port} is reserved by another Tomcat instance. Please choose a different port.`
                });
            }
        }
        
        return conflicts;
    }

    /**
     * 生成端口使用报告
     */
    async generatePortUsageReport(portRange: { start: number; end: number }): Promise<{
        totalPorts: number;
        availablePorts: number;
        usedPorts: PortUsageInfo[];
        reservedPorts: { port: number; instanceId: string }[];
    }> {
        const totalPorts = portRange.end - portRange.start + 1;
        const usedPorts: PortUsageInfo[] = [];
        const reservedPortsMap = this.portManager.getReservedPorts();
        const reservedPorts: { port: number; instanceId: string }[] = [];
        
        for (let port = portRange.start; port <= portRange.end; port++) {
            // 检查是否被预留
            if (reservedPortsMap.has(port)) {
                reservedPorts.push({
                    port,
                    instanceId: reservedPortsMap.get(port)!
                });
                continue;
            }
            
            // 检查是否被系统进程使用
            const isAvailable = await this.isPortAvailable(port);
            if (!isAvailable) {
                const usageInfo = await this.getPortUsageInfo(port);
                if (usageInfo) {
                    usedPorts.push(usageInfo);
                }
            }
        }
        
        const availablePorts = totalPorts - usedPorts.length - reservedPorts.length;
        
        return {
            totalPorts,
            availablePorts,
            usedPorts,
            reservedPorts
        };
    }

    /**
     * 建议端口配置
     */
    async suggestPortConfiguration(): Promise<{
        httpPort: number;
        httpsPort: number;
        ajpPort: number;
        jmxPort: number;
        shutdownPort: number;
    } | null> {
        const portRanges = this.portManager.getPortRanges();
        
        const httpPort = await this.findNextAvailablePort(portRanges.httpStart);
        if (!httpPort) return null;
        
        const httpsPort = await this.findNextAvailablePort(portRanges.httpsStart);
        if (!httpsPort) return null;
        
        const ajpPort = await this.findNextAvailablePort(portRanges.ajpStart);
        if (!ajpPort) return null;
        
        const jmxPort = await this.findNextAvailablePort(portRanges.jmxStart);
        if (!jmxPort) return null;
        
        const shutdownPort = await this.findNextAvailablePort(portRanges.shutdownStart);
        if (!shutdownPort) return null;
        
        return {
            httpPort,
            httpsPort,
            ajpPort,
            jmxPort,
            shutdownPort
        };
    }

    /**
     * 查找下一个可用端口
     */
    private async findNextAvailablePort(startPort: number, maxAttempts: number = 100): Promise<number | null> {
        for (let i = 0; i < maxAttempts; i++) {
            const port = startPort + i;
            if (port > 65535) break;
            
            const isAvailable = await this.isPortAvailable(port);
            if (isAvailable) {
                const portInfo = await this.portManager.getPortStatus(port);
                if (portInfo.status === PortStatus.AVAILABLE) {
                    return port;
                }
            }
        }
        return null;
    }
}
