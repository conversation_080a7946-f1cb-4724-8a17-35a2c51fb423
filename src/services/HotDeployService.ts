import * as vscode from "vscode";
import * as fs from "fs";
import * as path from "path";
import { ProjectConfiguration } from "../models/ProjectConfiguration";
import { ProjectDeploymentService } from "./ProjectDeploymentService";

/**
 * 文件变化事件接口
 */
export interface FileChangeEvent {
  type: "created" | "modified" | "deleted";
  filePath: string;
  projectId: string;
  timestamp: Date;
}

/**
 * 热部署任务接口
 */
export interface HotDeployTask {
  projectId: string;
  instanceId: string;
  scheduledAt: Date;
  files: string[];
}

/**
 * 热更新服务
 */
export class HotDeployService {
  private static instance: HotDeployService;
  private deploymentService: ProjectDeploymentService;
  private fileWatchers: Map<string, vscode.FileSystemWatcher> = new Map();
  private pendingTasks: Map<string, HotDeployTask> = new Map();
  private deploymentTimers: Map<string, NodeJS.Timeout> = new Map();
  private projects: Map<string, ProjectConfiguration> = new Map();

  private constructor() {
    this.deploymentService = ProjectDeploymentService.getInstance();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): HotDeployService {
    if (!HotDeployService.instance) {
      HotDeployService.instance = new HotDeployService();
    }
    return HotDeployService.instance;
  }

  /**
   * 启动项目的热部署监听
   */
  startWatching(project: ProjectConfiguration): void {
    const projectId = project.getId();

    // 如果已经在监听，先停止
    this.stopWatching(projectId);

    if (!project.isHotDeployEnabled()) {
      return;
    }

    this.projects.set(projectId, project);

    // 创建文件监听器
    const projectPath = project.getProjectPath();
    const watchPattern = new vscode.RelativePattern(projectPath, "**/*");
    const watcher = vscode.workspace.createFileSystemWatcher(watchPattern);

    // 监听文件变化事件
    watcher.onDidCreate((uri) =>
      this.handleFileChange("created", uri, project)
    );
    watcher.onDidChange((uri) =>
      this.handleFileChange("modified", uri, project)
    );
    watcher.onDidDelete((uri) =>
      this.handleFileChange("deleted", uri, project)
    );

    this.fileWatchers.set(projectId, watcher);

    console.log(
      `Started hot deploy watching for project: ${project.getName()}`
    );
  }

  /**
   * 停止项目的热部署监听
   */
  stopWatching(projectId: string): void {
    // 停止文件监听
    const watcher = this.fileWatchers.get(projectId);
    if (watcher) {
      watcher.dispose();
      this.fileWatchers.delete(projectId);
    }

    // 清除待处理的任务
    this.pendingTasks.delete(projectId);

    // 清除定时器
    const timer = this.deploymentTimers.get(projectId);
    if (timer) {
      clearTimeout(timer);
      this.deploymentTimers.delete(projectId);
    }

    // 移除项目引用
    this.projects.delete(projectId);

    console.log(`Stopped hot deploy watching for project: ${projectId}`);
  }

  /**
   * 停止所有监听
   */
  stopAllWatching(): void {
    for (const projectId of this.fileWatchers.keys()) {
      this.stopWatching(projectId);
    }
  }

  /**
   * 手动触发热部署
   */
  async triggerHotDeploy(projectId: string): Promise<void> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error(`Project ${projectId} not found`);
    }

    const instanceId = project.getTomcatInstanceId();
    if (!instanceId) {
      throw new Error(
        `Project ${project.getName()} is not associated with any Tomcat instance`
      );
    }

    await this.performHotDeploy(project, instanceId);
  }

  /**
   * 处理文件变化事件
   */
  private handleFileChange(
    type: "created" | "modified" | "deleted",
    uri: vscode.Uri,
    project: ProjectConfiguration
  ): void {
    const filePath = uri.fsPath;
    const projectId = project.getId();

    // 检查文件是否需要监听
    if (!project.shouldWatchFile(filePath)) {
      return;
    }

    console.log(`File ${type}: ${filePath}`);

    // 创建文件变化事件
    const changeEvent: FileChangeEvent = {
      type,
      filePath,
      projectId,
      timestamp: new Date(),
    };

    // 处理文件变化
    this.processFileChange(changeEvent, project);
  }

  /**
   * 处理文件变化
   */
  private processFileChange(
    event: FileChangeEvent,
    project: ProjectConfiguration
  ): void {
    const projectId = project.getId();
    const instanceId = project.getTomcatInstanceId();

    if (!instanceId) {
      console.log(
        `Project ${project.getName()} is not associated with any Tomcat instance`
      );
      return;
    }

    // 获取或创建待处理任务
    let task = this.pendingTasks.get(projectId);
    if (!task) {
      task = {
        projectId,
        instanceId,
        scheduledAt: new Date(),
        files: [],
      };
      this.pendingTasks.set(projectId, task);
    }

    // 添加文件到任务
    if (!task.files.includes(event.filePath)) {
      task.files.push(event.filePath);
    }

    // 重置部署定时器
    this.scheduleDeployment(project);
  }

  /**
   * 调度部署任务
   */
  private scheduleDeployment(project: ProjectConfiguration): void {
    const projectId = project.getId();
    const delay = project.getDeployDelay();

    // 清除现有定时器
    const existingTimer = this.deploymentTimers.get(projectId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 创建新的定时器
    const timer = setTimeout(async () => {
      try {
        await this.executeScheduledDeployment(projectId);
      } catch (error) {
        console.error(
          `Hot deploy failed for project ${project.getName()}:`,
          error
        );
        vscode.window.showErrorMessage(`Hot deploy failed: ${error}`);
      }
    }, delay);

    this.deploymentTimers.set(projectId, timer);
  }

  /**
   * 执行调度的部署任务
   */
  private async executeScheduledDeployment(projectId: string): Promise<void> {
    const task = this.pendingTasks.get(projectId);
    const project = this.projects.get(projectId);

    if (!task || !project) {
      return;
    }

    console.log(
      `Executing hot deploy for project ${project.getName()}, files: ${
        task.files.length
      }`
    );

    try {
      await this.performHotDeploy(project, task.instanceId);

      // 显示成功消息
      vscode.window.showInformationMessage(
        `Hot deploy completed for ${project.getName()}`
      );

      // 刷新Tomcat Explorer视图
      vscode.commands.executeCommand("tomcatManager.refresh");
    } catch (error) {
      console.error(`Hot deploy failed:`, error);
      throw error;
    } finally {
      // 清理任务
      this.pendingTasks.delete(projectId);
      this.deploymentTimers.delete(projectId);
    }
  }

  /**
   * 执行热部署
   */
  private async performHotDeploy(
    project: ProjectConfiguration,
    instanceId: string
  ): Promise<void> {
    // 现在使用exploded WAR，可以进行真正的增量更新
    console.log(`Performing incremental hot deploy...`);

    try {
      await this.performIncrementalUpdate(project, instanceId);
      console.log(`Hot deploy completed successfully for ${project.getName()}`);
    } catch (error) {
      console.warn(
        `Incremental update failed, falling back to full deployment:`,
        error
      );

      // 如果增量更新失败，回退到完整部署
      const result = await this.deploymentService.deployProject(
        project,
        instanceId
      );
      if (result.status !== "success") {
        throw new Error(result.message);
      }

      console.log(
        `Hot deploy completed with full deployment for ${project.getName()}`
      );
    }
  }

  /**
   * 执行增量更新
   */
  private async performIncrementalUpdate(
    project: ProjectConfiguration,
    instanceId: string
  ): Promise<void> {
    const task = this.pendingTasks.get(project.getId());
    if (!task) {
      return;
    }

    // 根据文件类型执行不同的更新策略
    const staticFiles = task.files.filter((file) => this.isStaticFile(file));
    const javaFiles = task.files.filter((file) => this.isJavaFile(file));
    const configFiles = task.files.filter((file) => this.isConfigFile(file));

    console.log(`Hot deploy analysis for ${project.getName()}:`);
    console.log(`- Static files: ${staticFiles.length}`);
    console.log(`- Java files: ${javaFiles.length}`);
    console.log(`- Config files: ${configFiles.length}`);

    // 如果有Java文件或配置文件变化，直接进行完整部署
    // 因为这些变化通常需要重启应用或重新编译
    if (javaFiles.length > 0 || configFiles.length > 0) {
      console.log(
        `Java or config files changed, performing full deployment...`
      );
      throw new Error("Java or config files changed, need full deployment");
    }

    // 只有静态文件变化时才进行增量更新
    if (staticFiles.length > 0) {
      await this.updateStaticFilesToExplodedWar(
        project,
        instanceId,
        staticFiles
      );
    }
  }

  /**
   * 更新静态文件到exploded WAR目录
   */
  private async updateStaticFilesToExplodedWar(
    project: ProjectConfiguration,
    instanceId: string,
    files: string[]
  ): Promise<void> {
    console.log(
      `Updating ${files.length} static files for project ${project.getName()}`
    );

    const fs = require("fs");
    const path = require("path");

    // 获取Tomcat实例
    const instance =
      this.deploymentService["instanceManager"].getInstance(instanceId);
    if (!instance) {
      throw new Error(`Tomcat instance ${instanceId} not found`);
    }

    // 获取exploded WAR部署目录
    const config = instance.getConfiguration();
    const contextPath = project.getContextPath();
    let deployDir: string;

    if (contextPath === "ROOT") {
      deployDir = path.join(config.instancePath, "webapps", "ROOT");
    } else {
      const contextName = contextPath.startsWith("/")
        ? contextPath.substring(1)
        : contextPath;
      deployDir = path.join(config.instancePath, "webapps", contextName);
    }

    // 确保exploded WAR目录存在
    if (!fs.existsSync(deployDir)) {
      throw new Error(`Exploded WAR directory ${deployDir} does not exist`);
    }

    console.log(`Updating static files in exploded WAR: ${deployDir}`);

    // 处理每个文件
    let successCount = 0;
    for (const filePath of files) {
      try {
        if (!fs.existsSync(filePath) || !fs.statSync(filePath).isFile()) {
          console.warn(`Skipping non-existent file: ${filePath}`);
          continue;
        }

        const success = await this.copyStaticFileToExplodedWar(
          filePath,
          project,
          deployDir
        );

        if (success) {
          successCount++;
        }
      } catch (error) {
        console.error(`Failed to update static file ${filePath}:`, error);
        // 继续处理其他文件，不抛出错误
      }
    }

    console.log(
      `Successfully updated ${successCount}/${files.length} static files in exploded WAR`
    );
  }

  /**
   * 复制单个静态文件到exploded WAR目录
   */
  private async copyStaticFileToExplodedWar(
    filePath: string,
    project: ProjectConfiguration,
    deployDir: string
  ): Promise<boolean> {
    const fs = require("fs");
    const path = require("path");

    try {
      // 计算相对路径
      const projectPath = project.getProjectPath();
      let relativePath = path.relative(projectPath, filePath);

      // 处理Maven/Gradle项目的资源路径
      if (relativePath.startsWith("src/main/webapp/")) {
        relativePath = relativePath.substring("src/main/webapp/".length);
      } else if (relativePath.startsWith("src/main/resources/")) {
        relativePath = relativePath.substring("src/main/resources/".length);
        relativePath = path.join("WEB-INF/classes", relativePath);
      } else {
        // 对于其他路径，尝试智能映射
        console.warn(`Unexpected file path structure: ${relativePath}`);
        // 如果不是标准的Maven/Gradle结构，跳过增量更新
        return false;
      }

      const targetPath = path.join(deployDir, relativePath);
      const targetDir = path.dirname(targetPath);

      // 确保目标目录存在
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // 直接复制文件到exploded WAR目录
      // 由于是exploded WAR，可以直接覆盖文件，Tomcat会立即生效
      fs.copyFileSync(filePath, targetPath);

      console.log(`✅ Hot deployed static file: ${relativePath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to hot deploy static file ${filePath}:`, error);
      return false;
    }
  }

  /**
   * 检查是否是静态文件
   */
  private isStaticFile(filePath: string): boolean {
    const staticExtensions = [
      ".html",
      ".css",
      ".js",
      ".jsp",
      ".png",
      ".jpg",
      ".gif",
      ".svg",
    ];
    const ext = path.extname(filePath).toLowerCase();
    return staticExtensions.includes(ext);
  }

  /**
   * 检查是否是Java文件
   */
  private isJavaFile(filePath: string): boolean {
    return path.extname(filePath).toLowerCase() === ".java";
  }

  /**
   * 检查是否是配置文件
   */
  private isConfigFile(filePath: string): boolean {
    const configExtensions = [".xml", ".properties", ".yml", ".yaml"];
    const ext = path.extname(filePath).toLowerCase();
    return configExtensions.includes(ext);
  }

  /**
   * 获取监听状态
   */
  getWatchingStatus(): {
    projectId: string;
    projectName: string;
    isWatching: boolean;
  }[] {
    const status: {
      projectId: string;
      projectName: string;
      isWatching: boolean;
    }[] = [];

    for (const [projectId, project] of this.projects.entries()) {
      status.push({
        projectId,
        projectName: project.getName(),
        isWatching: this.fileWatchers.has(projectId),
      });
    }

    return status;
  }

  /**
   * 获取待处理任务
   */
  getPendingTasks(): HotDeployTask[] {
    return Array.from(this.pendingTasks.values());
  }
}
