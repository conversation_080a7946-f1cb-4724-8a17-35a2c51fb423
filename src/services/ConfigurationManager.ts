import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { TomcatInstanceConfiguration } from '../models/TomcatInstance';
import { ProjectConfigurationData } from '../models/ProjectConfiguration';

/**
 * 全局配置接口
 */
export interface GlobalConfiguration {
    baseTomcatPath: string;
    defaultJrePath: string;
    defaultBrowser: string;
    customBrowserPath: string;
    autoOpenBrowser: boolean;
    portRanges: {
        httpStart: number;
        httpsStart: number;
        ajpStart: number;
        jmxStart: number;
        shutdownStart: number;
    };
}

/**
 * 配置管理器
 */
export class ConfigurationManager {
    private static instance: ConfigurationManager;
    private configPath: string;

    private constructor() {
        this.configPath = this.getConfigurationPath();
        this.ensureConfigDirectory();
    }

    /**
     * 获取单例实例
     */
    static getInstance(): ConfigurationManager {
        if (!ConfigurationManager.instance) {
            ConfigurationManager.instance = new ConfigurationManager();
        }
        return ConfigurationManager.instance;
    }

    /**
     * 获取全局配置
     */
    getGlobalConfiguration(): GlobalConfiguration {
        const config = vscode.workspace.getConfiguration('tomcatManager');
        
        return {
            baseTomcatPath: config.get<string>('baseTomcatPath') || '',
            defaultJrePath: config.get<string>('defaultJrePath') || '',
            defaultBrowser: config.get<string>('defaultBrowser') || 'default',
            customBrowserPath: config.get<string>('customBrowserPath') || '',
            autoOpenBrowser: config.get<boolean>('autoOpenBrowser') || true,
            portRanges: config.get('portRange') || {
                httpStart: 8080,
                httpsStart: 8443,
                ajpStart: 8009,
                jmxStart: 9999,
                shutdownStart: 8005
            }
        };
    }

    /**
     * 更新全局配置
     */
    async updateGlobalConfiguration(updates: Partial<GlobalConfiguration>): Promise<void> {
        const config = vscode.workspace.getConfiguration('tomcatManager');
        
        for (const [key, value] of Object.entries(updates)) {
            if (key === 'portRanges') {
                await config.update('portRange', value, vscode.ConfigurationTarget.Global);
            } else {
                await config.update(key, value, vscode.ConfigurationTarget.Global);
            }
        }
    }

    /**
     * 加载Tomcat实例配置
     */
    async loadInstances(): Promise<TomcatInstanceConfiguration[]> {
        const instancesFile = path.join(this.configPath, 'instances.json');
        
        if (!fs.existsSync(instancesFile)) {
            return [];
        }

        try {
            const content = fs.readFileSync(instancesFile, 'utf8');
            const data = JSON.parse(content);
            
            // 转换日期字符串为Date对象
            return data.map((config: any) => ({
                ...config,
                createdAt: new Date(config.createdAt),
                lastModified: new Date(config.lastModified),
                deployedApps: config.deployedApps.map((app: any) => ({
                    ...app,
                    deployedAt: app.deployedAt ? new Date(app.deployedAt) : undefined
                }))
            }));
        } catch (error) {
            console.error('Failed to load instances configuration:', error);
            return [];
        }
    }

    /**
     * 保存Tomcat实例配置
     */
    async saveInstances(instances: TomcatInstanceConfiguration[]): Promise<void> {
        const instancesFile = path.join(this.configPath, 'instances.json');
        
        try {
            const content = JSON.stringify(instances, null, 2);
            fs.writeFileSync(instancesFile, content);
        } catch (error) {
            console.error('Failed to save instances configuration:', error);
            throw error;
        }
    }

    /**
     * 加载项目配置
     */
    async loadProjects(): Promise<ProjectConfigurationData[]> {
        const projectsFile = path.join(this.configPath, 'projects.json');
        
        if (!fs.existsSync(projectsFile)) {
            return [];
        }

        try {
            const content = fs.readFileSync(projectsFile, 'utf8');
            const data = JSON.parse(content);
            
            // 转换日期字符串为Date对象
            return data.map((config: any) => ({
                ...config,
                createdAt: new Date(config.createdAt),
                lastModified: new Date(config.lastModified),
                lastDeployed: config.lastDeployed ? new Date(config.lastDeployed) : undefined
            }));
        } catch (error) {
            console.error('Failed to load projects configuration:', error);
            return [];
        }
    }

    /**
     * 保存项目配置
     */
    async saveProjects(projects: ProjectConfigurationData[]): Promise<void> {
        const projectsFile = path.join(this.configPath, 'projects.json');
        
        try {
            const content = JSON.stringify(projects, null, 2);
            fs.writeFileSync(projectsFile, content);
        } catch (error) {
            console.error('Failed to save projects configuration:', error);
            throw error;
        }
    }

    /**
     * 导出配置
     */
    async exportConfiguration(exportPath: string): Promise<void> {
        const instances = await this.loadInstances();
        const projects = await this.loadProjects();
        const globalConfig = this.getGlobalConfiguration();

        const exportData = {
            version: '1.0.0',
            exportedAt: new Date().toISOString(),
            globalConfiguration: globalConfig,
            instances,
            projects
        };

        try {
            const content = JSON.stringify(exportData, null, 2);
            fs.writeFileSync(exportPath, content);
        } catch (error) {
            console.error('Failed to export configuration:', error);
            throw error;
        }
    }

    /**
     * 导入配置
     */
    async importConfiguration(importPath: string): Promise<void> {
        if (!fs.existsSync(importPath)) {
            throw new Error('Import file does not exist');
        }

        try {
            const content = fs.readFileSync(importPath, 'utf8');
            const importData = JSON.parse(content);

            // 验证导入数据格式
            if (!importData.version || !importData.instances || !importData.projects) {
                throw new Error('Invalid import file format');
            }

            // 导入实例配置
            if (importData.instances.length > 0) {
                await this.saveInstances(importData.instances);
            }

            // 导入项目配置
            if (importData.projects.length > 0) {
                await this.saveProjects(importData.projects);
            }

            // 导入全局配置
            if (importData.globalConfiguration) {
                await this.updateGlobalConfiguration(importData.globalConfiguration);
            }

        } catch (error) {
            console.error('Failed to import configuration:', error);
            throw error;
        }
    }

    /**
     * 备份配置
     */
    async backupConfiguration(): Promise<string> {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFileName = `tomcat-config-backup-${timestamp}.json`;
        const backupPath = path.join(this.configPath, 'backups', backupFileName);

        // 确保备份目录存在
        const backupDir = path.dirname(backupPath);
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }

        await this.exportConfiguration(backupPath);
        return backupPath;
    }

    /**
     * 清理旧备份
     */
    async cleanupOldBackups(maxBackups: number = 10): Promise<void> {
        const backupDir = path.join(this.configPath, 'backups');
        
        if (!fs.existsSync(backupDir)) {
            return;
        }

        try {
            const files = fs.readdirSync(backupDir)
                .filter(file => file.startsWith('tomcat-config-backup-') && file.endsWith('.json'))
                .map(file => ({
                    name: file,
                    path: path.join(backupDir, file),
                    mtime: fs.statSync(path.join(backupDir, file)).mtime
                }))
                .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

            // 删除超过最大数量的备份文件
            if (files.length > maxBackups) {
                const filesToDelete = files.slice(maxBackups);
                for (const file of filesToDelete) {
                    fs.unlinkSync(file.path);
                }
            }
        } catch (error) {
            console.error('Failed to cleanup old backups:', error);
        }
    }

    /**
     * 重置配置
     */
    async resetConfiguration(): Promise<void> {
        // 先备份当前配置
        await this.backupConfiguration();

        // 删除配置文件
        const instancesFile = path.join(this.configPath, 'instances.json');
        const projectsFile = path.join(this.configPath, 'projects.json');

        if (fs.existsSync(instancesFile)) {
            fs.unlinkSync(instancesFile);
        }

        if (fs.existsSync(projectsFile)) {
            fs.unlinkSync(projectsFile);
        }

        // 重置VSCode配置
        const config = vscode.workspace.getConfiguration('tomcatManager');
        await config.update('baseTomcatPath', undefined, vscode.ConfigurationTarget.Global);
        await config.update('defaultJrePath', undefined, vscode.ConfigurationTarget.Global);
        await config.update('defaultBrowser', 'default', vscode.ConfigurationTarget.Global);
        await config.update('customBrowserPath', undefined, vscode.ConfigurationTarget.Global);
        await config.update('autoOpenBrowser', true, vscode.ConfigurationTarget.Global);
    }

    /**
     * 获取配置路径
     */
    private getConfigurationPath(): string {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return path.join(workspaceFolder.uri.fsPath, '.vscode', 'tomcat-manager');
        }
        return path.join(require('os').homedir(), '.vscode-tomcat-manager');
    }

    /**
     * 确保配置目录存在
     */
    private ensureConfigDirectory(): void {
        if (!fs.existsSync(this.configPath)) {
            fs.mkdirSync(this.configPath, { recursive: true });
        }
    }
}
