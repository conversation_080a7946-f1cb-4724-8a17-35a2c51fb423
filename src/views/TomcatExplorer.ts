import * as vscode from 'vscode';
import * as path from 'path';
import { TomcatInstance, TomcatInstanceStatus, DeployedApplication } from '../models/TomcatInstance';
import { TomcatInstanceManager } from '../services/TomcatInstanceManager';

/**
 * 树节点类型枚举
 */
export enum TreeItemType {
    TOMCAT_INSTANCES = 'tomcatInstances',
    TOMCAT_INSTANCE = 'tomcatInstance',
    DEPLOYED_APPS = 'deployedApps',
    DEPLOYED_APP = 'deployedApp',
    INSTANCE_INFO = 'instanceInfo'
}

/**
 * Tomcat资源管理器树节点
 */
export class TomcatTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly itemType: TreeItemType,
        public readonly instanceId?: string,
        public readonly appId?: string
    ) {
        super(label, collapsibleState);
        this.contextValue = this.getContextValue();
        this.iconPath = this.getIconPath();
        this.tooltip = this.getTooltip();
    }

    private getContextValue(): string {
        if (this.itemType === TreeItemType.TOMCAT_INSTANCE && this.instanceId) {
            const manager = TomcatInstanceManager.getInstance();
            const instance = manager.getInstance(this.instanceId);
            if (instance) {
                const status = instance.getStatus();
                return `tomcatInstance-${status}`;
            }
        }
        return this.itemType;
    }

    private getIconPath(): vscode.ThemeIcon | undefined {
        switch (this.itemType) {
            case TreeItemType.TOMCAT_INSTANCES:
                return new vscode.ThemeIcon('server-environment');
            case TreeItemType.TOMCAT_INSTANCE:
                return this.getInstanceIcon();
            case TreeItemType.DEPLOYED_APPS:
                return new vscode.ThemeIcon('package');
            case TreeItemType.DEPLOYED_APP:
                return new vscode.ThemeIcon('globe');
            case TreeItemType.INSTANCE_INFO:
                return new vscode.ThemeIcon('info');
            default:
                return undefined;
        }
    }

    private getInstanceIcon(): vscode.ThemeIcon {
        if (this.instanceId) {
            const manager = TomcatInstanceManager.getInstance();
            const instance = manager.getInstance(this.instanceId);
            if (instance) {
                const status = instance.getStatus();
                switch (status) {
                    case TomcatInstanceStatus.RUNNING:
                        return new vscode.ThemeIcon('play-circle', new vscode.ThemeColor('charts.green'));
                    case TomcatInstanceStatus.STOPPED:
                        return new vscode.ThemeIcon('stop-circle', new vscode.ThemeColor('charts.red'));
                    case TomcatInstanceStatus.STARTING:
                        return new vscode.ThemeIcon('loading~spin', new vscode.ThemeColor('charts.yellow'));
                    case TomcatInstanceStatus.STOPPING:
                        return new vscode.ThemeIcon('loading~spin', new vscode.ThemeColor('charts.orange'));
                    case TomcatInstanceStatus.ERROR:
                        return new vscode.ThemeIcon('error', new vscode.ThemeColor('errorForeground'));
                    default:
                        return new vscode.ThemeIcon('circle-outline');
                }
            }
        }
        return new vscode.ThemeIcon('circle-outline');
    }

    private getTooltip(): string {
        if (this.itemType === TreeItemType.TOMCAT_INSTANCE && this.instanceId) {
            const manager = TomcatInstanceManager.getInstance();
            const instance = manager.getInstance(this.instanceId);
            if (instance) {
                const config = instance.getConfiguration();
                return `${config.name}\nStatus: ${config.status}\nHTTP Port: ${config.ports.httpPort}\nPath: ${config.instancePath}`;
            }
        }
        return this.label;
    }
}

/**
 * Tomcat资源管理器数据提供者
 */
export class TomcatExplorerProvider implements vscode.TreeDataProvider<TomcatTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TomcatTreeItem | undefined | null | void> = new vscode.EventEmitter<TomcatTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<TomcatTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private instanceManager: TomcatInstanceManager;

    constructor() {
        this.instanceManager = TomcatInstanceManager.getInstance();
    }

    /**
     * 刷新树视图
     */
    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    /**
     * 获取树节点
     */
    getTreeItem(element: TomcatTreeItem): vscode.TreeItem {
        return element;
    }

    /**
     * 获取子节点
     */
    getChildren(element?: TomcatTreeItem): Thenable<TomcatTreeItem[]> {
        if (!element) {
            // 根节点
            return Promise.resolve(this.getRootItems());
        }

        switch (element.itemType) {
            case TreeItemType.TOMCAT_INSTANCES:
                return Promise.resolve(this.getInstanceItems());
            case TreeItemType.TOMCAT_INSTANCE:
                return Promise.resolve(this.getInstanceChildren(element.instanceId!));
            case TreeItemType.DEPLOYED_APPS:
                return Promise.resolve(this.getDeployedAppItems(element.instanceId!));
            default:
                return Promise.resolve([]);
        }
    }

    /**
     * 获取根节点项
     */
    private getRootItems(): TomcatTreeItem[] {
        return [
            new TomcatTreeItem(
                'Tomcat Instances',
                vscode.TreeItemCollapsibleState.Expanded,
                TreeItemType.TOMCAT_INSTANCES
            )
        ];
    }

    /**
     * 获取实例节点项
     */
    private getInstanceItems(): TomcatTreeItem[] {
        const instances = this.instanceManager.getAllInstances();
        
        if (instances.length === 0) {
            return [
                new TomcatTreeItem(
                    'No Tomcat instances configured',
                    vscode.TreeItemCollapsibleState.None,
                    TreeItemType.INSTANCE_INFO
                )
            ];
        }

        return instances.map(instance => {
            const config = instance.getConfiguration();
            const statusIcon = this.getStatusIcon(config.status);
            const label = `${statusIcon} ${config.name}`;
            
            return new TomcatTreeItem(
                label,
                vscode.TreeItemCollapsibleState.Collapsed,
                TreeItemType.TOMCAT_INSTANCE,
                instance.getId()
            );
        });
    }

    /**
     * 获取实例子节点
     */
    private getInstanceChildren(instanceId: string): TomcatTreeItem[] {
        const instance = this.instanceManager.getInstance(instanceId);
        if (!instance) {
            return [];
        }

        const config = instance.getConfiguration();
        const children: TomcatTreeItem[] = [];

        // 实例信息
        children.push(
            new TomcatTreeItem(
                `HTTP Port: ${config.ports.httpPort}`,
                vscode.TreeItemCollapsibleState.None,
                TreeItemType.INSTANCE_INFO,
                instanceId
            ),
            new TomcatTreeItem(
                `HTTPS Port: ${config.ports.httpsPort}`,
                vscode.TreeItemCollapsibleState.None,
                TreeItemType.INSTANCE_INFO,
                instanceId
            ),
            new TomcatTreeItem(
                `Status: ${config.status}`,
                vscode.TreeItemCollapsibleState.None,
                TreeItemType.INSTANCE_INFO,
                instanceId
            )
        );

        // 部署的应用
        const deployedApps = instance.getDeployedApps();
        if (deployedApps.length > 0) {
            children.push(
                new TomcatTreeItem(
                    `Deployed Applications (${deployedApps.length})`,
                    vscode.TreeItemCollapsibleState.Expanded,
                    TreeItemType.DEPLOYED_APPS,
                    instanceId
                )
            );
        } else {
            children.push(
                new TomcatTreeItem(
                    'No deployed applications',
                    vscode.TreeItemCollapsibleState.None,
                    TreeItemType.DEPLOYED_APPS,
                    instanceId
                )
            );
        }

        return children;
    }

    /**
     * 获取部署应用节点项
     */
    private getDeployedAppItems(instanceId: string): TomcatTreeItem[] {
        const instance = this.instanceManager.getInstance(instanceId);
        if (!instance) {
            return [];
        }

        const deployedApps = instance.getDeployedApps();
        return deployedApps.map(app => {
            const statusIcon = app.status === 'deployed' ? '✅' : '❌';
            const label = `${statusIcon} ${app.name} (${app.contextPath})`;
            
            const item = new TomcatTreeItem(
                label,
                vscode.TreeItemCollapsibleState.None,
                TreeItemType.DEPLOYED_APP,
                instanceId,
                app.id
            );

            // 设置点击命令
            item.command = {
                command: 'tomcatManager.openInBrowser',
                title: 'Open in Browser',
                arguments: [instanceId, app.contextPath]
            };

            return item;
        });
    }

    /**
     * 获取状态图标
     */
    private getStatusIcon(status: TomcatInstanceStatus): string {
        switch (status) {
            case TomcatInstanceStatus.RUNNING:
                return '🟢';
            case TomcatInstanceStatus.STOPPED:
                return '🔴';
            case TomcatInstanceStatus.STARTING:
                return '🟡';
            case TomcatInstanceStatus.STOPPING:
                return '🟠';
            case TomcatInstanceStatus.ERROR:
                return '❌';
            default:
                return '⚪';
        }
    }
}

/**
 * Tomcat资源管理器
 */
export class TomcatExplorer {
    private treeDataProvider: TomcatExplorerProvider;
    private treeView: vscode.TreeView<TomcatTreeItem>;

    constructor(context: vscode.ExtensionContext) {
        this.treeDataProvider = new TomcatExplorerProvider();
        this.treeView = vscode.window.createTreeView('tomcatExplorer', {
            treeDataProvider: this.treeDataProvider,
            showCollapseAll: true
        });

        // 注册刷新命令
        context.subscriptions.push(
            vscode.commands.registerCommand('tomcatManager.refresh', () => {
                this.refresh();
            })
        );

        // 监听实例变化
        this.setupInstanceChangeListener();
    }

    /**
     * 刷新视图
     */
    refresh(): void {
        this.treeDataProvider.refresh();
    }

    /**
     * 显示特定实例
     */
    async revealInstance(instanceId: string): Promise<void> {
        // 刷新视图以确保最新数据
        this.refresh();
        
        // 这里可以添加展开到特定实例的逻辑
        // VSCode TreeView API 目前对此支持有限
    }

    /**
     * 设置实例变化监听器
     */
    private setupInstanceChangeListener(): void {
        // 这里可以添加监听实例状态变化的逻辑
        // 当实例状态发生变化时自动刷新视图
        
        // 定期刷新视图以更新状态
        setInterval(() => {
            this.refresh();
        }, 5000); // 每5秒刷新一次
    }

    /**
     * 获取选中的实例
     */
    getSelectedInstance(): TomcatTreeItem | undefined {
        return this.treeView.selection[0];
    }

    /**
     * 销毁资源
     */
    dispose(): void {
        this.treeView.dispose();
    }
}
