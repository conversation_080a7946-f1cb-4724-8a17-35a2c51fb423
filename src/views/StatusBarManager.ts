import * as vscode from "vscode";
import { TomcatInstanceManager } from "../services/TomcatInstanceManager";
import { TomcatInstanceStatus } from "../models/TomcatInstance";
import { PortManager } from "../models/PortManager";

/**
 * 状态栏项类型枚举
 */
export enum StatusBarItemType {
  INSTANCES_COUNT = "instancesCount",
  RUNNING_INSTANCES = "runningInstances",
  PORT_USAGE = "portUsage",
  QUICK_ACTIONS = "quickActions",
}

/**
 * 状态栏管理器
 */
export class StatusBarManager {
  private static instance: StatusBarManager;
  private statusBarItems: Map<StatusBarItemType, vscode.StatusBarItem> =
    new Map();
  private instanceManager: TomcatInstanceManager;
  private portManager: PortManager;
  private updateTimer?: NodeJS.Timeout;

  private constructor() {
    this.instanceManager = TomcatInstanceManager.getInstance();
    this.portManager = PortManager.getInstance();
    this.createStatusBarItems();
    this.startPeriodicUpdate();
    this.setupInstanceChangeListener();
  }

  /**
   * 设置实例变化监听器
   */
  private setupInstanceChangeListener(): void {
    // 监听实例状态变化事件
    this.instanceManager.onInstanceStatusChanged((event) => {
      console.log(
        `StatusBar: Instance ${event.instanceId} status changed: ${event.oldStatus} → ${event.newStatus}`
      );
      // 实时更新状态栏
      this.updateStatusBar();
    });
  }

  /**
   * 获取单例实例
   */
  static getInstance(): StatusBarManager {
    if (!StatusBarManager.instance) {
      StatusBarManager.instance = new StatusBarManager();
    }
    return StatusBarManager.instance;
  }

  /**
   * 创建状态栏项
   */
  private createStatusBarItems(): void {
    // 实例数量状态栏项
    const instancesCountItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Left,
      100
    );
    instancesCountItem.command = "tomcatManager.showInstancesOverview";
    this.statusBarItems.set(
      StatusBarItemType.INSTANCES_COUNT,
      instancesCountItem
    );

    // 运行中实例状态栏项
    const runningInstancesItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Left,
      99
    );
    runningInstancesItem.command = "tomcatManager.showRunningInstances";
    this.statusBarItems.set(
      StatusBarItemType.RUNNING_INSTANCES,
      runningInstancesItem
    );

    // 端口使用状态栏项
    const portUsageItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Left,
      98
    );
    portUsageItem.command = "tomcatManager.showPortUsage";
    this.statusBarItems.set(StatusBarItemType.PORT_USAGE, portUsageItem);

    // 快速操作状态栏项
    const quickActionsItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Left,
      97
    );
    quickActionsItem.command = "tomcatManager.showQuickActions";
    this.statusBarItems.set(StatusBarItemType.QUICK_ACTIONS, quickActionsItem);

    // 显示所有状态栏项
    this.showAllItems();
  }

  /**
   * 更新状态栏
   */
  updateStatusBar(): void {
    this.updateInstancesCount();
    this.updateRunningInstances();
    this.updatePortUsage();
    this.updateQuickActions();
  }

  /**
   * 更新实例数量
   */
  private updateInstancesCount(): void {
    const item = this.statusBarItems.get(StatusBarItemType.INSTANCES_COUNT);
    if (!item) return;

    const instances = this.instanceManager.getAllInstances();
    const totalCount = instances.length;

    if (totalCount === 0) {
      item.text = "$(server) No Tomcat Instances";
      item.tooltip = "No Tomcat instances configured. Click to add one.";
      item.command = "tomcatManager.addTomcatInstance";
    } else {
      item.text = `$(server) ${totalCount} Instance${
        totalCount > 1 ? "s" : ""
      }`;
      item.tooltip = `${totalCount} Tomcat instance${
        totalCount > 1 ? "s" : ""
      } configured. Click to view overview.`;
      item.command = "tomcatManager.showInstancesOverview";
    }
  }

  /**
   * 更新运行中实例
   */
  private updateRunningInstances(): void {
    const item = this.statusBarItems.get(StatusBarItemType.RUNNING_INSTANCES);
    if (!item) return;

    const instances = this.instanceManager.getAllInstances();
    const runningInstances = instances.filter(
      (instance) => instance.getStatus() === TomcatInstanceStatus.RUNNING
    );
    const runningCount = runningInstances.length;

    if (runningCount === 0) {
      item.text = "$(stop-circle) None Running";
      item.tooltip = "No Tomcat instances are currently running.";
      item.color = new vscode.ThemeColor("statusBarItem.errorForeground");
    } else {
      item.text = `$(play-circle) ${runningCount} Running`;
      item.tooltip = `${runningCount} Tomcat instance${
        runningCount > 1 ? "s" : ""
      } running. Click to view details.`;
      item.color = new vscode.ThemeColor("statusBarItem.prominentForeground");
    }
  }

  /**
   * 更新端口使用情况
   */
  private updatePortUsage(): void {
    const item = this.statusBarItems.get(StatusBarItemType.PORT_USAGE);
    if (!item) return;

    const reservedPorts = this.portManager.getReservedPorts();
    const portCount = reservedPorts.size;

    if (portCount === 0) {
      item.text = "$(ports-view-icon) No Ports Reserved";
      item.tooltip = "No ports are currently reserved by Tomcat instances.";
    } else {
      item.text = `$(ports-view-icon) ${portCount} Port${
        portCount > 1 ? "s" : ""
      } Reserved`;
      item.tooltip = `${portCount} port${
        portCount > 1 ? "s" : ""
      } reserved by Tomcat instances. Click to view details.`;
    }
  }

  /**
   * 更新快速操作
   */
  private updateQuickActions(): void {
    const item = this.statusBarItems.get(StatusBarItemType.QUICK_ACTIONS);
    if (!item) return;

    const instances = this.instanceManager.getAllInstances();
    const runningInstances = instances.filter(
      (instance) => instance.getStatus() === TomcatInstanceStatus.RUNNING
    );

    if (runningInstances.length > 0) {
      item.text = "$(debug-stop) Stop All";
      item.tooltip = "Stop all running Tomcat instances";
      item.command = "tomcatManager.stopAllInstances";
      item.color = new vscode.ThemeColor("statusBarItem.errorForeground");
    } else if (instances.length > 0) {
      item.text = "$(play) Start All";
      item.tooltip = "Start all Tomcat instances";
      item.command = "tomcatManager.startAllInstances";
      item.color = new vscode.ThemeColor("statusBarItem.prominentForeground");
    } else {
      item.text = "$(add) Add Instance";
      item.tooltip = "Add a new Tomcat instance";
      item.command = "tomcatManager.addTomcatInstance";
      item.color = undefined;
    }
  }

  /**
   * 显示进度
   */
  showProgress(message: string, task: Thenable<any>): void {
    vscode.window.withProgress(
      {
        location: vscode.ProgressLocation.Window,
        title: message,
        cancellable: false,
      },
      () => task
    );
  }

  /**
   * 显示实例操作进度
   */
  showInstanceProgress(
    instanceName: string,
    operation: string,
    task: Thenable<any>
  ): void {
    const message = `${operation} ${instanceName}...`;
    this.showProgress(message, task);
  }

  /**
   * 显示部署进度
   */
  showDeploymentProgress(
    projectName: string,
    instanceName: string,
    task: Thenable<any>
  ): void {
    const message = `Deploying ${projectName} to ${instanceName}...`;
    this.showProgress(message, task);
  }

  /**
   * 显示成功消息
   */
  showSuccessMessage(message: string): void {
    vscode.window.showInformationMessage(message);
  }

  /**
   * 显示错误消息
   */
  showErrorMessage(message: string): void {
    vscode.window.showErrorMessage(message);
  }

  /**
   * 显示警告消息
   */
  showWarningMessage(message: string): void {
    vscode.window.showWarningMessage(message);
  }

  /**
   * 显示实例状态变化通知
   */
  showInstanceStatusChange(
    instanceName: string,
    oldStatus: TomcatInstanceStatus,
    newStatus: TomcatInstanceStatus
  ): void {
    const statusText = this.getStatusText(newStatus);
    const message = `${instanceName} is now ${statusText}`;

    if (newStatus === TomcatInstanceStatus.RUNNING) {
      this.showSuccessMessage(message);
    } else if (newStatus === TomcatInstanceStatus.ERROR) {
      this.showErrorMessage(message);
    } else {
      vscode.window.showInformationMessage(message);
    }
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: TomcatInstanceStatus): string {
    switch (status) {
      case TomcatInstanceStatus.RUNNING:
        return "running";
      case TomcatInstanceStatus.STOPPED:
        return "stopped";
      case TomcatInstanceStatus.STARTING:
        return "starting";
      case TomcatInstanceStatus.STOPPING:
        return "stopping";
      case TomcatInstanceStatus.ERROR:
        return "in error state";
      default:
        return "unknown";
    }
  }

  /**
   * 显示所有状态栏项
   */
  private showAllItems(): void {
    for (const item of this.statusBarItems.values()) {
      item.show();
    }
  }

  /**
   * 隐藏所有状态栏项
   */
  hideAllItems(): void {
    for (const item of this.statusBarItems.values()) {
      item.hide();
    }
  }

  /**
   * 开始定期更新
   */
  private startPeriodicUpdate(): void {
    // 立即更新一次
    this.updateStatusBar();

    // 每3秒更新一次
    this.updateTimer = setInterval(() => {
      this.updateStatusBar();
    }, 3000);
  }

  /**
   * 停止定期更新
   */
  private stopPeriodicUpdate(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = undefined;
    }
  }

  /**
   * 销毁资源
   */
  dispose(): void {
    this.stopPeriodicUpdate();

    for (const item of this.statusBarItems.values()) {
      item.dispose();
    }

    this.statusBarItems.clear();
  }
}
