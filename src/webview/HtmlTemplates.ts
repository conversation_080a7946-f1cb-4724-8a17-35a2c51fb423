// HTML模板生成器 - 不需要导入vscode模块

/**
 * HTML模板生成器
 */
export class HtmlTemplates {
  /**
   * 获取基础HTML模板
   */
  static getBaseTemplate(
    title: string,
    content: string,
    scripts: string = ""
  ): string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 13px;
            line-height: 1.5;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--vscode-foreground);
            margin-bottom: 8px;
        }
        
        .header p {
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--vscode-foreground);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-size: 13px;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--vscode-focusBorder);
            box-shadow: 0 0 0 1px var(--vscode-focusBorder);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .button-primary {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }
        
        .button-primary:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .button-secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        
        .button-secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--vscode-panel-border);
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background-color: var(--vscode-inputValidation-infoBackground);
            border-left-color: var(--vscode-inputValidation-infoBorder);
            color: var(--vscode-inputValidation-infoForeground);
        }
        
        .alert-error {
            background-color: var(--vscode-inputValidation-errorBackground);
            border-left-color: var(--vscode-inputValidation-errorBorder);
            color: var(--vscode-inputValidation-errorForeground);
        }
        
        .alert-warning {
            background-color: var(--vscode-inputValidation-warningBackground);
            border-left-color: var(--vscode-inputValidation-warningBorder);
            color: var(--vscode-inputValidation-warningForeground);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            border: 2px solid var(--vscode-progressBar-background);
            border-top: 2px solid var(--vscode-progressBar-foreground);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .card {
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .card-header {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--vscode-foreground);
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--vscode-panel-border);
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: var(--vscode-descriptionForeground);
            transition: all 0.2s;
        }
        
        .tab.active {
            color: var(--vscode-foreground);
            border-bottom-color: var(--vscode-focusBorder);
        }
        
        .tab:hover {
            color: var(--vscode-foreground);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .help-text {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .port-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running {
            background-color: #4CAF50;
        }
        
        .status-stopped {
            background-color: #f44336;
        }
        
        .status-starting {
            background-color: #FF9800;
        }
        
        .project-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
        }
        
        .project-item {
            padding: 12px;
            border-bottom: 1px solid var(--vscode-panel-border);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .project-item:hover {
            background-color: var(--vscode-list-hoverBackground);
        }
        
        .project-item.selected {
            background-color: var(--vscode-list-activeSelectionBackground);
            color: var(--vscode-list-activeSelectionForeground);
        }
        
        .project-item:last-child {
            border-bottom: none;
        }
        
        .project-name {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .project-path {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
        }
        
        .project-type {
            display: inline-block;
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        ${content}
    </div>
    
    <script>
        const vscode = acquireVsCodeApi();
        
        // 通用工具函数
        function showLoading() {
            const loading = document.querySelector('.loading');
            if (loading) loading.classList.add('show');
        }
        
        function hideLoading() {
            const loading = document.querySelector('.loading');
            if (loading) loading.classList.remove('show');
        }
        
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alert-container');
            if (alertContainer) {
                alertContainer.innerHTML = \`<div class="alert alert-\${type}">\${message}</div>\`;
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 自定义确认对话框
        function showConfirm(message, onConfirm, onCancel) {
            // 创建确认对话框HTML
            const confirmHtml = \`
                <div id="custom-confirm" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                ">
                    <div style="
                        background: var(--vscode-editor-background);
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 4px;
                        padding: 20px;
                        min-width: 300px;
                        max-width: 500px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    ">
                        <div style="
                            margin-bottom: 20px;
                            color: var(--vscode-foreground);
                            font-size: 14px;
                            line-height: 1.5;
                        ">\${message}</div>
                        <div style="
                            display: flex;
                            gap: 10px;
                            justify-content: flex-end;
                        ">
                            <button id="confirm-cancel" class="button button-secondary">取消</button>
                            <button id="confirm-ok" class="button button-primary">确定</button>
                        </div>
                    </div>
                </div>
            \`;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', confirmHtml);

            // 绑定事件
            document.getElementById('confirm-ok').onclick = function() {
                document.getElementById('custom-confirm').remove();
                if (onConfirm) onConfirm();
            };

            document.getElementById('confirm-cancel').onclick = function() {
                document.getElementById('custom-confirm').remove();
                if (onCancel) onCancel();
            };

            // 点击背景关闭
            document.getElementById('custom-confirm').onclick = function(e) {
                if (e.target.id === 'custom-confirm') {
                    document.getElementById('custom-confirm').remove();
                    if (onCancel) onCancel();
                }
            };
        }
        
        function sendMessage(command, data) {
            console.log('Sending message:', command, data);
            if (typeof vscode !== 'undefined') {
                vscode.postMessage({ command, data });
            } else {
                console.error('vscode object is not available');
                showAlert('VSCode API不可用，请重新打开面板', 'error');
            }
        }
        
        // 标签页切换
        function initTabs() {
            const tabs = document.querySelectorAll('.tab');
            const contents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const target = tab.dataset.tab;
                    
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));
                    
                    tab.classList.add('active');
                    document.getElementById(target).classList.add('active');
                });
            });
        }
        
        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            handleMessage(message);
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initTabs();
            if (typeof initPage === 'function') {
                initPage();
            }
        });
        
        ${scripts}
    </script>
</body>
</html>`;
  }

  /**
   * 获取实例创建向导HTML
   */
  static getInstanceWizardHtml(): string {
    const content = `
            <div class="header">
                <h1>🚀 创建Tomcat实例</h1>
                <p>创建一个新的Tomcat服务器实例，配置端口、JVM参数等设置</p>

                <!-- 测试按钮 -->
                <div style="margin-top: 10px; padding: 10px; background: var(--vscode-editor-inactiveSelectionBackground); border-radius: 4px;">
                    <button type="button" onclick="testFunction()" style="margin-right: 10px;">🧪 测试按钮</button>
                    <span id="test-result" style="color: var(--vscode-descriptionForeground);"></span>
                </div>
            </div>
            
            <div id="alert-container"></div>
            
            <form id="instance-form">
                <div class="card">
                    <div class="card-header">基本信息</div>
                    
                    <div class="form-group">
                        <label for="name">实例名称 *</label>
                        <input type="text" id="name" name="name" placeholder="例如: MyApp-Dev" required>
                        <div class="help-text">为实例指定一个有意义的名称</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">描述</label>
                        <textarea id="description" name="description" placeholder="实例的详细描述（可选）"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="baseTomcatPath">基础Tomcat路径 *</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="text" id="baseTomcatPath" name="baseTomcatPath" placeholder="/usr/local/tomcat" required style="flex: 1;">
                            <button type="button" class="button button-secondary" onclick="selectTomcatPath()" style="white-space: nowrap;">
                                📁 选择文件夹
                            </button>
                        </div>
                        <div class="help-text">指向Tomcat安装目录的路径</div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">端口配置</div>
                    
                    <div class="button-group" style="margin-top: 0; padding-top: 0; border-top: none;">
                        <button type="button" class="button button-secondary" onclick="suggestPorts()">
                            🎯 自动分配端口
                        </button>
                    </div>
                    
                    <div class="port-grid">
                        <div class="form-group">
                            <label for="httpPort">HTTP端口</label>
                            <input type="number" id="httpPort" name="httpPort" value="8080" min="1024" max="65535">
                        </div>
                        
                        <div class="form-group">
                            <label for="httpsPort">HTTPS端口</label>
                            <input type="number" id="httpsPort" name="httpsPort" value="8443" min="1024" max="65535">
                        </div>
                        
                        <div class="form-group">
                            <label for="ajpPort">AJP端口</label>
                            <input type="number" id="ajpPort" name="ajpPort" value="8009" min="1024" max="65535">
                        </div>
                        
                        <div class="form-group">
                            <label for="jmxPort">JMX端口</label>
                            <input type="number" id="jmxPort" name="jmxPort" value="9999" min="1024" max="65535">
                        </div>
                        
                        <div class="form-group">
                            <label for="shutdownPort">管理端口</label>
                            <input type="number" id="shutdownPort" name="shutdownPort" value="8005" min="1024" max="65535">
                        </div>
                    </div>
                    
                    <div id="port-validation"></div>
                </div>
                
                <div class="card">
                    <div class="card-header">JVM配置</div>
                    
                    <div class="form-group">
                        <label for="jrePath">JRE路径</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="text" id="jrePath" name="jrePath" placeholder="留空使用默认JRE" style="flex: 1;">
                            <button type="button" class="button button-secondary" onclick="loadDefaultJrePath()" style="white-space: nowrap;">
                                🔄 读取默认
                            </button>
                            <button type="button" class="button button-secondary" onclick="selectJrePath()" style="white-space: nowrap;">
                                📁 选择文件夹
                            </button>
                        </div>
                        <div class="help-text">指定此实例使用的Java运行环境，留空使用系统默认</div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="minHeapSize">最小堆内存</label>
                            <input type="text" id="minHeapSize" name="minHeapSize" value="256m" placeholder="256m">
                        </div>

                        <div class="form-group">
                            <label for="maxHeapSize">最大堆内存</label>
                            <input type="text" id="maxHeapSize" name="maxHeapSize" value="512m" placeholder="512m">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="jvmArgs">JVM参数</label>
                        <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                            <button type="button" class="button button-secondary" onclick="addCommonJvmArgs()" style="font-size: 12px;">
                                📋 常用参数
                            </button>
                            <button type="button" class="button button-secondary" onclick="addJava9PlusArgs()" style="font-size: 12px;">
                                ☕ Java 9+ 模块参数
                            </button>
                            <button type="button" class="button button-secondary" onclick="clearJvmArgs()" style="font-size: 12px;">
                                🗑️ 清空
                            </button>
                        </div>
                        <textarea id="jvmArgs" name="jvmArgs" rows="8" placeholder="每行一个JVM参数，例如：
-Dfile.encoding=UTF-8
-Dspring.profiles.active=dev
-Dlogback.configurationFile=/path/to/logback.xml
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED
--add-opens=java.base/java.math=ALL-UNNAMED

# 以 # 开头的行为注释，会被忽略"></textarea>
                        <div class="help-text">每行输入一个JVM参数，支持系统属性(-D)、模块开放(--add-opens)等参数。以#开头的行为注释。</div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">浏览器设置</div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="autoOpenBrowser" name="autoOpenBrowser" checked>
                        <label for="autoOpenBrowser">启动时自动打开浏览器</label>
                    </div>
                    
                    <div class="form-group">
                        <label for="browserType">浏览器类型</label>
                        <select id="browserType" name="browserType">
                            <option value="default">系统默认</option>
                            <option value="chrome">Google Chrome</option>
                            <option value="firefox">Mozilla Firefox</option>
                            <option value="safari">Safari</option>
                            <option value="edge">Microsoft Edge</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="customBrowserPath" style="display: none;">
                        <label for="customPath">自定义浏览器路径</label>
                        <input type="text" id="customPath" name="customPath" placeholder="浏览器可执行文件路径">
                    </div>
                    
                    <div class="form-group">
                        <label for="defaultPage">默认启动页面</label>
                        <input type="text" id="defaultPage" name="defaultPage" placeholder="例如: /index.html">
                        <div class="help-text">启动后在浏览器中打开的页面路径</div>
                    </div>
                </div>
                
                <div class="loading">
                    <div class="spinner"></div>
                    正在创建实例...
                </div>
                
                <div class="button-group">
                    <button type="submit" class="button button-primary">创建实例</button>
                    <button type="button" class="button button-secondary" onclick="window.close()">取消</button>
                </div>
            </form>
        `;

    const scripts = `
            // 测试基本功能
            console.log('Script loaded successfully');

            // 将函数添加到全局作用域
            window.testFunction = function() {
                console.log('Test button clicked!');
                const resultElement = document.getElementById('test-result');
                if (resultElement) {
                    resultElement.textContent = '✅ 按钮点击正常！时间: ' + new Date().toLocaleTimeString();
                    resultElement.style.color = 'var(--vscode-testing-iconPassed)';
                } else {
                    console.error('test-result element not found');
                }

                // 测试VSCode API
                if (typeof vscode !== 'undefined') {
                    console.log('VSCode API available');
                    try {
                        vscode.postMessage({ command: 'test', data: { message: 'Test message from WebView' } });
                        console.log('Test message sent to VSCode');
                    } catch (error) {
                        console.error('Error sending test message:', error);
                    }
                } else {
                    console.error('VSCode API not available');
                    if (resultElement) {
                        resultElement.textContent = '❌ VSCode API不可用';
                        resultElement.style.color = 'var(--vscode-testing-iconFailed)';
                    }
                }
            };

            // 将其他函数也添加到全局作用域
            window.selectTomcatPath = function() {
                console.log('selectTomcatPath clicked');
                sendMessage('selectTomcatPath', {});
            };

            window.selectJrePath = function() {
                sendMessage('selectJrePath', {});
            };

            window.loadDefaultJrePath = function() {
                sendMessage('loadDefaultJrePath', {});
            };

            window.suggestPorts = function() {
                sendMessage('suggestPorts', {});
            };

            window.validatePorts = function() {
                const ports = {
                    httpPort: parseInt(document.getElementById('httpPort').value),
                    httpsPort: parseInt(document.getElementById('httpsPort').value),
                    ajpPort: parseInt(document.getElementById('ajpPort').value),
                    jmxPort: parseInt(document.getElementById('jmxPort').value),
                    shutdownPort: parseInt(document.getElementById('shutdownPort').value)
                };

                sendMessage('validatePorts', ports);
            };

            window.addCommonJvmArgs = function() {
                const jvmArgsTextarea = document.getElementById('jvmArgs');
                const commonArgs = [
                    '# 常用JVM参数',
                    '-Dfile.encoding=UTF-8',
                    '-Duser.timezone=Asia/Shanghai',
                    '-Djava.awt.headless=true',
                    '-Djava.security.egd=file:/dev/./urandom',
                    '-server'
                ].join('\\n');

                if (jvmArgsTextarea.value.trim()) {
                    jvmArgsTextarea.value += '\\n\\n' + commonArgs;
                } else {
                    jvmArgsTextarea.value = commonArgs;
                }
            };

            window.addJava9PlusArgs = function() {
                const jvmArgsTextarea = document.getElementById('jvmArgs');
                const java9Args = [
                    '# Java 9+ 模块系统参数',
                    '--add-opens=java.base/java.lang.reflect=ALL-UNNAMED',
                    '--add-opens=java.base/java.math=ALL-UNNAMED',
                    '--add-opens=java.base/java.lang=ALL-UNNAMED',
                    '--add-opens=java.base/java.io=ALL-UNNAMED',
                    '--add-opens=java.base/java.util=ALL-UNNAMED',
                    '--add-opens=java.base/java.util.concurrent=ALL-UNNAMED',
                    '--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED',
                    '--add-opens=java.base/java.lang.ref=ALL-UNNAMED',
                    '--add-opens=java.base/sun.nio.fs=ALL-UNNAMED',
                    '--add-opens=java.base/java.nio.file=ALL-UNNAMED',
                    '--add-opens=java.base/java.net=ALL-UNNAMED'
                ].join('\\n');

                if (jvmArgsTextarea.value.trim()) {
                    jvmArgsTextarea.value += '\\n\\n' + java9Args;
                } else {
                    jvmArgsTextarea.value = java9Args;
                }
            };

            window.clearJvmArgs = function() {
                document.getElementById('jvmArgs').value = '';
            };

            window.createInstance = function() {
                const formData = new FormData(document.getElementById('instance-form'));

                // 处理JVM参数
                const jvmArgsText = formData.get('jvmArgs') || '';
                const jvmArgs = jvmArgsText
                    .split('\\n')
                    .map(arg => arg.trim())
                    .filter(arg => arg.length > 0 && !arg.startsWith('#'));

                const data = {
                    name: formData.get('name'),
                    description: formData.get('description'),
                    baseTomcatPath: formData.get('baseTomcatPath'),
                    ports: {
                        httpPort: parseInt(formData.get('httpPort')),
                        httpsPort: parseInt(formData.get('httpsPort')),
                        ajpPort: parseInt(formData.get('ajpPort')),
                        jmxPort: parseInt(formData.get('jmxPort')),
                        shutdownPort: parseInt(formData.get('shutdownPort'))
                    },
                    jvm: {
                        jrePath: formData.get('jrePath') || '',
                        minHeapSize: formData.get('minHeapSize'),
                        maxHeapSize: formData.get('maxHeapSize'),
                        additionalArgs: jvmArgs
                    },
                    browser: {
                        type: formData.get('browserType'),
                        autoOpen: formData.has('autoOpenBrowser'),
                        defaultPage: formData.get('defaultPage'),
                        customPath: formData.get('customPath')
                    }
                };

                showLoading();
                sendMessage('createInstance', data);
            };

            function initPage() {
                console.log('initPage called');

                try {
                    // 浏览器类型变化处理
                    const browserTypeElement = document.getElementById('browserType');
                    if (browserTypeElement) {
                        browserTypeElement.addEventListener('change', function() {
                            console.log('Browser type changed:', this.value);
                            const customPath = document.getElementById('customBrowserPath');
                            if (customPath) {
                                if (this.value === 'custom') {
                                    customPath.style.display = 'block';
                                } else {
                                    customPath.style.display = 'none';
                                }
                            }
                        });
                    }

                    // 端口变化时验证
                    const portInputs = document.querySelectorAll('input[type="number"]');
                    portInputs.forEach(input => {
                        input.addEventListener('blur', validatePorts);
                    });

                    // 表单提交
                    const formElement = document.getElementById('instance-form');
                    if (formElement) {
                        formElement.addEventListener('submit', function(e) {
                            console.log('Form submit prevented');
                            e.preventDefault();
                            createInstance();
                        });
                    }

                    console.log('initPage completed successfully');
                } catch (error) {
                    console.error('Error in initPage:', error);
                }
            }
            
            function validatePorts() {
                const ports = {
                    httpPort: parseInt(document.getElementById('httpPort').value),
                    httpsPort: parseInt(document.getElementById('httpsPort').value),
                    ajpPort: parseInt(document.getElementById('ajpPort').value),
                    jmxPort: parseInt(document.getElementById('jmxPort').value),
                    shutdownPort: parseInt(document.getElementById('shutdownPort').value)
                };
                
                sendMessage('validatePorts', ports);
            }
            
            function handleMessage(message) {
                switch (message.command) {
                    case 'instanceCreated':
                        hideLoading();
                        if (message.data.success) {
                            showAlert('实例创建成功！', 'success');
                            setTimeout(() => window.close(), 2000);
                        } else {
                            showAlert('创建失败: ' + message.data.error, 'error');
                        }
                        break;
                    case 'portsValidated':
                        const validation = document.getElementById('port-validation');
                        if (message.data.errors.length > 0) {
                            validation.innerHTML = '<div class="alert alert-error">端口配置错误: ' + 
                                message.data.errors.join(', ') + '</div>';
                        } else {
                            validation.innerHTML = '<div class="alert alert-success">端口配置有效</div>';
                        }
                        break;
                    case 'portsSuggested':
                        if (message.data.ports) {
                            const ports = message.data.ports;
                            document.getElementById('httpPort').value = ports.httpPort;
                            document.getElementById('httpsPort').value = ports.httpsPort;
                            document.getElementById('ajpPort').value = ports.ajpPort;
                            document.getElementById('jmxPort').value = ports.jmxPort;
                            document.getElementById('shutdownPort').value = ports.shutdownPort;
                            showAlert('已自动分配可用端口', 'success');
                        }
                        break;
                    case 'tomcatPathSelected':
                        console.log('tomcatPathSelected message received:', message.data);
                        if (message.data.path) {
                            document.getElementById('baseTomcatPath').value = message.data.path;
                            showAlert('Tomcat路径已选择: ' + message.data.path, 'success');
                        } else {
                            console.log('No path in tomcatPathSelected message');
                        }
                        break;
                    case 'jrePathSelected':
                        if (message.data.path) {
                            document.getElementById('jrePath').value = message.data.path;
                            showAlert('JRE路径已选择', 'success');
                        }
                        break;
                    case 'defaultJrePathLoaded':
                        if (message.data.path) {
                            document.getElementById('jrePath').value = message.data.path;
                            showAlert('已加载默认JRE路径', 'success');
                        } else {
                            showAlert('未找到默认JRE路径', 'warning');
                        }
                        break;
                }
            }
        `;

    return this.getBaseTemplate("创建Tomcat实例", content, scripts);
  }

  /**
   * 获取实例配置界面HTML
   */
  static getInstanceConfigHtml(): string {
    const content = `
            <div class="header">
                <h1>⚙️ 配置Tomcat实例</h1>
                <p>修改实例的端口、JVM参数、浏览器设置等配置</p>
            </div>

            <form id="config-form">
                <div class="card">
                    <div class="card-header">基本信息</div>

                    <div class="form-group">
                        <label for="name">实例名称 *</label>
                        <input type="text" id="name" name="name" required>
                        <div class="help-text">实例的显示名称</div>
                    </div>

                    <div class="form-group">
                        <label for="description">描述</label>
                        <textarea id="description" name="description" rows="2" placeholder="实例描述信息（可选）"></textarea>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">端口配置</div>

                    <div class="button-group" style="margin-top: 0; padding-top: 0; border-top: none;">
                        <button type="button" class="button button-secondary" onclick="suggestPorts()">
                            🎯 自动分配端口
                        </button>
                        <button type="button" class="button button-secondary" onclick="validatePorts()">
                            ✅ 验证端口
                        </button>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="httpPort">HTTP端口 *</label>
                            <input type="number" id="httpPort" name="httpPort" min="1024" max="65535" required>
                        </div>
                        <div class="form-group">
                            <label for="httpsPort">HTTPS端口</label>
                            <input type="number" id="httpsPort" name="httpsPort" min="1024" max="65535">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="ajpPort">AJP端口</label>
                            <input type="number" id="ajpPort" name="ajpPort" min="1024" max="65535">
                        </div>
                        <div class="form-group">
                            <label for="jmxPort">JMX端口</label>
                            <input type="number" id="jmxPort" name="jmxPort" min="1024" max="65535">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="shutdownPort">关闭端口 *</label>
                        <input type="number" id="shutdownPort" name="shutdownPort" min="1024" max="65535" required>
                        <div class="help-text">用于关闭Tomcat服务器的端口</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">JVM配置</div>

                    <div class="form-group">
                        <label for="jrePath">JRE路径</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="text" id="jrePath" name="jrePath" placeholder="留空使用默认JRE" style="flex: 1;">
                            <button type="button" class="button button-secondary" onclick="loadDefaultJrePath()" style="white-space: nowrap;">
                                🔄 读取默认
                            </button>
                            <button type="button" class="button button-secondary" onclick="selectJrePath()" style="white-space: nowrap;">
                                📁 选择文件夹
                            </button>
                        </div>
                        <div class="help-text">指定Java运行环境路径，留空使用系统默认</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="minHeapSize">最小堆内存</label>
                            <input type="text" id="minHeapSize" name="minHeapSize" placeholder="256m" pattern="[0-9]+[mMgG]">
                            <div class="help-text">例如: 256m, 1g</div>
                        </div>
                        <div class="form-group">
                            <label for="maxHeapSize">最大堆内存</label>
                            <input type="text" id="maxHeapSize" name="maxHeapSize" placeholder="512m" pattern="[0-9]+[mMgG]">
                            <div class="help-text">例如: 512m, 2g</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="jvmArgs">JVM参数</label>
                        <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                            <button type="button" class="button button-secondary" onclick="addCommonJvmArgs()">
                                📋 常用参数
                            </button>
                            <button type="button" class="button button-secondary" onclick="addJava9PlusArgs()">
                                ☕ Java 9+ 模块参数
                            </button>
                            <button type="button" class="button button-secondary" onclick="clearJvmArgs()">
                                🗑️ 清空
                            </button>
                        </div>
                        <textarea id="jvmArgs" name="jvmArgs" rows="8" placeholder="每行一个JVM参数..."></textarea>
                        <div class="help-text">每行输入一个JVM参数，支持系统属性(-D)、模块开放(--add-opens)等参数。以#开头的行为注释。</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">浏览器设置</div>

                    <div class="form-group">
                        <label for="browserType">浏览器类型</label>
                        <select id="browserType" name="browserType">
                            <option value="default">系统默认浏览器</option>
                            <option value="chrome">Google Chrome</option>
                            <option value="firefox">Mozilla Firefox</option>
                            <option value="safari">Safari</option>
                            <option value="edge">Microsoft Edge</option>
                            <option value="custom">自定义浏览器</option>
                        </select>
                    </div>

                    <div class="form-group" id="customBrowserPath" style="display: none;">
                        <label for="customPath">自定义浏览器路径</label>
                        <input type="text" id="customPath" name="customPath" placeholder="/path/to/browser">
                        <div class="help-text">自定义浏览器的可执行文件路径</div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="autoOpenBrowser" name="autoOpenBrowser" checked>
                        <label for="autoOpenBrowser">启动时自动打开浏览器</label>
                    </div>

                    <div class="form-group">
                        <label for="defaultPage">默认页面</label>
                        <input type="text" id="defaultPage" name="defaultPage" placeholder="留空打开根页面">
                        <div class="help-text">启动后自动打开的页面路径，例如: /admin</div>
                    </div>
                </div>

                <div class="button-group">
                    <button type="submit" class="button button-primary">
                        💾 保存配置
                    </button>
                    <button type="button" class="button button-secondary" onclick="window.close()">
                        取消
                    </button>
                </div>
            </form>
        `;

    const scripts = `
            // 当前实例配置
            let currentInstanceId = null;
            let originalConfig = null;

            // 测试基本功能
            console.log('Instance config script loaded successfully');

            // 将函数添加到全局作用域
            window.suggestPorts = function() {
                sendMessage('suggestPorts', {});
            };

            window.validatePorts = function() {
                const ports = {
                    httpPort: parseInt(document.getElementById('httpPort').value),
                    httpsPort: parseInt(document.getElementById('httpsPort').value),
                    ajpPort: parseInt(document.getElementById('ajpPort').value),
                    jmxPort: parseInt(document.getElementById('jmxPort').value),
                    shutdownPort: parseInt(document.getElementById('shutdownPort').value)
                };

                sendMessage('validatePorts', ports);
            };

            window.selectJrePath = function() {
                sendMessage('selectJrePath', {});
            };

            window.loadDefaultJrePath = function() {
                sendMessage('loadDefaultJrePath', {});
            };

            window.addCommonJvmArgs = function() {
                const jvmArgsTextarea = document.getElementById('jvmArgs');
                const commonArgs = [
                    '# 常用JVM参数',
                    '-Dfile.encoding=UTF-8',
                    '-Duser.timezone=Asia/Shanghai',
                    '-Djava.awt.headless=true',
                    '-Djava.security.egd=file:/dev/./urandom',
                    '-server'
                ].join('\\n');

                if (jvmArgsTextarea.value.trim()) {
                    jvmArgsTextarea.value += '\\n\\n' + commonArgs;
                } else {
                    jvmArgsTextarea.value = commonArgs;
                }
            };

            window.addJava9PlusArgs = function() {
                const jvmArgsTextarea = document.getElementById('jvmArgs');
                const java9Args = [
                    '# Java 9+ 模块系统参数',
                    '--add-opens=java.base/java.lang.reflect=ALL-UNNAMED',
                    '--add-opens=java.base/java.math=ALL-UNNAMED',
                    '--add-opens=java.base/java.lang=ALL-UNNAMED',
                    '--add-opens=java.base/java.io=ALL-UNNAMED',
                    '--add-opens=java.base/java.util=ALL-UNNAMED',
                    '--add-opens=java.base/java.util.concurrent=ALL-UNNAMED',
                    '--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED',
                    '--add-opens=java.base/java.lang.ref=ALL-UNNAMED',
                    '--add-opens=java.base/sun.nio.fs=ALL-UNNAMED',
                    '--add-opens=java.base/java.nio.file=ALL-UNNAMED',
                    '--add-opens=java.base/java.net=ALL-UNNAMED'
                ].join('\\n');

                if (jvmArgsTextarea.value.trim()) {
                    jvmArgsTextarea.value += '\\n\\n' + java9Args;
                } else {
                    jvmArgsTextarea.value = java9Args;
                }
            };

            window.clearJvmArgs = function() {
                document.getElementById('jvmArgs').value = '';
            };

            window.updateInstance = function() {
                const formData = new FormData(document.getElementById('config-form'));

                // 处理JVM参数
                const jvmArgsText = formData.get('jvmArgs') || '';
                const jvmArgs = jvmArgsText
                    .split('\\n')
                    .map(arg => arg.trim())
                    .filter(arg => arg.length > 0 && !arg.startsWith('#'));

                const updates = {
                    name: formData.get('name'),
                    description: formData.get('description'),
                    ports: {
                        httpPort: parseInt(formData.get('httpPort')),
                        httpsPort: parseInt(formData.get('httpsPort')),
                        ajpPort: parseInt(formData.get('ajpPort')),
                        jmxPort: parseInt(formData.get('jmxPort')),
                        shutdownPort: parseInt(formData.get('shutdownPort'))
                    },
                    jvm: {
                        jrePath: formData.get('jrePath') || '',
                        minHeapSize: formData.get('minHeapSize'),
                        maxHeapSize: formData.get('maxHeapSize'),
                        additionalArgs: jvmArgs
                    },
                    browser: {
                        type: formData.get('browserType'),
                        autoOpen: formData.has('autoOpenBrowser'),
                        defaultPage: formData.get('defaultPage'),
                        customPath: formData.get('customPath')
                    }
                };

                showLoading();
                sendMessage('updateInstance', { instanceId: currentInstanceId, updates: updates });
            };

            function initPage() {
                console.log('initPage called');

                try {
                    // 浏览器类型变化处理
                    const browserTypeElement = document.getElementById('browserType');
                    if (browserTypeElement) {
                        browserTypeElement.addEventListener('change', function() {
                            console.log('Browser type changed:', this.value);
                            const customPath = document.getElementById('customBrowserPath');
                            if (customPath) {
                                if (this.value === 'custom') {
                                    customPath.style.display = 'block';
                                } else {
                                    customPath.style.display = 'none';
                                }
                            }
                        });
                    }

                    // 端口变化时验证
                    const portInputs = document.querySelectorAll('input[type="number"]');
                    portInputs.forEach(input => {
                        input.addEventListener('blur', validatePorts);
                    });

                    // 表单提交
                    const formElement = document.getElementById('config-form');
                    if (formElement) {
                        formElement.addEventListener('submit', function(e) {
                            console.log('Form submit prevented');
                            e.preventDefault();
                            updateInstance();
                        });
                    }

                    // 实例数据将通过消息自动发送，无需手动请求

                    console.log('initPage completed successfully');
                } catch (error) {
                    console.error('Error in initPage:', error);
                }
            }

            function loadInstanceConfig(instance) {
                console.log('Loading instance config:', instance);
                originalConfig = instance;
                currentInstanceId = instance.id; // 设置当前实例ID

                // 填充基本信息
                document.getElementById('name').value = instance.name || '';
                document.getElementById('description').value = instance.description || '';

                // 填充端口配置
                document.getElementById('httpPort').value = instance.ports.httpPort || '';
                document.getElementById('httpsPort').value = instance.ports.httpsPort || '';
                document.getElementById('ajpPort').value = instance.ports.ajpPort || '';
                document.getElementById('jmxPort').value = instance.ports.jmxPort || '';
                document.getElementById('shutdownPort').value = instance.ports.shutdownPort || '';

                // 填充JVM配置
                document.getElementById('jrePath').value = instance.jvm.jrePath || '';
                document.getElementById('minHeapSize').value = instance.jvm.minHeapSize || '';
                document.getElementById('maxHeapSize').value = instance.jvm.maxHeapSize || '';

                // 填充JVM参数
                if (instance.jvm.additionalArgs && instance.jvm.additionalArgs.length > 0) {
                    document.getElementById('jvmArgs').value = instance.jvm.additionalArgs.join('\\n');
                }

                // 填充浏览器配置
                document.getElementById('browserType').value = instance.browser.type || 'default';
                document.getElementById('autoOpenBrowser').checked = instance.browser.autoOpen !== false;
                document.getElementById('defaultPage').value = instance.browser.defaultPage || '';
                document.getElementById('customPath').value = instance.browser.customPath || '';

                // 触发浏览器类型变化事件
                const browserTypeElement = document.getElementById('browserType');
                if (browserTypeElement) {
                    browserTypeElement.dispatchEvent(new Event('change'));
                }
            }

            function handleMessage(message) {
                console.log('Received message:', message);
                switch (message.command) {
                    case 'instanceLoaded':
                        console.log('Instance loaded message received:', message.data);
                        if (message.data.instance) {
                            loadInstanceConfig(message.data.instance);
                        } else {
                            console.error('No instance data in instanceLoaded message');
                        }
                        break;
                    case 'instanceUpdated':
                        hideLoading();
                        if (message.data.success) {
                            showAlert('实例配置更新成功！', 'success');
                            setTimeout(() => window.close(), 2000);
                        } else {
                            showAlert('更新失败: ' + message.data.error, 'error');
                        }
                        break;
                    case 'portsValidated':
                        if (message.data.valid) {
                            showAlert('端口配置有效', 'success');
                        } else {
                            showAlert('端口配置无效: ' + message.data.errors.join(', '), 'error');
                        }
                        break;
                    case 'portsSuggested':
                        if (message.data.ports) {
                            document.getElementById('httpPort').value = message.data.ports.httpPort;
                            document.getElementById('httpsPort').value = message.data.ports.httpsPort;
                            document.getElementById('ajpPort').value = message.data.ports.ajpPort;
                            document.getElementById('jmxPort').value = message.data.ports.jmxPort;
                            document.getElementById('shutdownPort').value = message.data.ports.shutdownPort;
                            showAlert('已自动分配可用端口', 'success');
                        }
                        break;
                    case 'jrePathSelected':
                        if (message.data.path) {
                            document.getElementById('jrePath').value = message.data.path;
                            showAlert('JRE路径已选择', 'success');
                        }
                        break;
                    case 'defaultJrePathLoaded':
                        if (message.data.path) {
                            document.getElementById('jrePath').value = message.data.path;
                            showAlert('已加载默认JRE路径', 'success');
                        }
                        break;
                }
            }
        `;

    return this.getBaseTemplate("配置Tomcat实例", content, scripts);
  }

  /**
   * 获取项目部署界面HTML
   */
  static getProjectDeployHtml(instanceData?: any): string {
    const content = `
            <div class="header">
                <h1>📦 部署项目</h1>
                <p>将Java Web项目部署到Tomcat实例: <strong>${
                  instanceData?.name || "Unknown"
                }</strong></p>
            </div>

            <div id="alert-container"></div>

            <div class="tabs">
                <div class="tab active" data-tab="select-project">选择项目</div>
                <div class="tab" data-tab="configure-deployment">配置部署</div>
                <div class="tab" data-tab="deploy-progress">部署进度</div>
            </div>

            <div id="select-project" class="tab-content active">
                <div class="card">
                    <div class="card-header">工作区项目</div>

                    <div id="project-list" class="project-list">
                        <div style="text-align: center; padding: 40px; color: var(--vscode-descriptionForeground);">
                            正在加载工作区中的Java Web项目...<br>
                            <small style="color: var(--vscode-descriptionForeground); opacity: 0.8;">
                                只会显示WAR包装类型的Web项目，自动过滤JAR包装的普通Java项目
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div id="configure-deployment" class="tab-content">
                <div class="card">
                    <div class="card-header">部署配置</div>

                    <div id="selected-project-info" style="display: none;">
                        <div class="alert alert-success">
                            <strong>已选择项目:</strong> <span id="selected-project-name"></span><br>
                            <strong>项目路径:</strong> <span id="selected-project-path"></span><br>
                            <strong>项目类型:</strong> <span id="selected-project-type"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="contextPath">上下文路径</label>
                        <select id="contextPath" name="contextPath">
                            <option value="ROOT">ROOT (根路径部署)</option>
                            <option value="custom">自定义路径</option>
                        </select>
                        <div class="help-text">选择应用的访问路径</div>
                    </div>

                    <div class="form-group" id="customContextPath" style="display: none;">
                        <label for="customPath">自定义上下文路径</label>
                        <input type="text" id="customPath" name="customPath" placeholder="/myapp">
                        <div class="help-text">必须以"/"开头，例如: /myapp</div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="enableHotDeploy" name="enableHotDeploy" checked>
                        <label for="enableHotDeploy">启用热部署</label>
                    </div>

                    <div id="hot-deploy-config" class="card" style="margin-top: 15px;">
                        <div class="card-header">热部署配置</div>

                        <div class="form-group">
                            <label for="watchExtensions">监听文件扩展名</label>
                            <input type="text" id="watchExtensions" name="watchExtensions"
                                   value="java,jsp,html,css,js,xml,properties">
                            <div class="help-text">用逗号分隔，例如: java,jsp,html</div>
                        </div>

                        <div class="form-group">
                            <label for="excludeDirectories">排除目录</label>
                            <input type="text" id="excludeDirectories" name="excludeDirectories"
                                   value="target,build,node_modules,.git">
                            <div class="help-text">用逗号分隔，例如: target,build</div>
                        </div>

                        <div class="form-group">
                            <label for="deployDelay">部署延迟 (毫秒)</label>
                            <input type="number" id="deployDelay" name="deployDelay" value="1000" min="0">
                            <div class="help-text">文件变化后等待多长时间再部署</div>
                        </div>

                        <div class="checkbox-group">
                            <input type="checkbox" id="restartApp" name="restartApp">
                            <label for="restartApp">文件变化时重启应用</label>
                        </div>
                    </div>
                </div>
            </div>

            <div id="deploy-progress" class="tab-content">
                <div class="card">
                    <div class="card-header">部署进度</div>

                    <div id="deploy-status">
                        <div style="text-align: center; padding: 40px; color: var(--vscode-descriptionForeground);">
                            配置完成后点击"开始部署"按钮
                        </div>
                    </div>

                    <div id="deploy-log" style="display: none;">
                        <div class="form-group">
                            <label>部署日志</label>
                            <textarea id="log-content" readonly style="height: 200px; font-family: monospace;"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="loading">
                <div class="spinner"></div>
                <span id="loading-text">正在处理...</span>
            </div>

            <div class="button-group">
                <button type="button" id="prev-btn" class="button button-secondary" onclick="previousStep()" style="display: none;">
                    上一步
                </button>
                <button type="button" id="next-btn" class="button button-primary" onclick="nextStep()" disabled>
                    下一步
                </button>
                <button type="button" id="deploy-btn" class="button button-primary" onclick="startDeploy()" style="display: none;">
                    开始部署
                </button>
                <button type="button" id="redeploy-btn" class="button button-secondary" onclick="redeploy()" style="display: none;">
                    重新部署
                </button>
                <button type="button" class="button button-secondary" onclick="window.close()">
                    取消
                </button>
            </div>
        `;

    const scripts = `
            let currentStep = 0;
            let selectedProject = null;
            let projects = [];
            let isDeploying = false;
            let isDeployed = false;

            const steps = ['select-project', 'configure-deployment', 'deploy-progress'];

            function initPage() {
                // 上下文路径变化处理
                document.getElementById('contextPath').addEventListener('change', function() {
                    const customPath = document.getElementById('customContextPath');
                    if (this.value === 'custom') {
                        customPath.style.display = 'block';
                    } else {
                        customPath.style.display = 'none';
                    }
                });

                // 热部署开关
                document.getElementById('enableHotDeploy').addEventListener('change', function() {
                    const config = document.getElementById('hot-deploy-config');
                    if (this.checked) {
                        config.style.display = 'block';
                    } else {
                        config.style.display = 'none';
                    }
                });

                updateStepButtons();

                // 自动扫描项目
                scanProjects();
            }

            function scanProjects() {
                showLoading();
                document.getElementById('loading-text').textContent = '正在扫描项目...';
                sendMessage('scanProjects', {});
            }

            function selectProject(index) {
                selectedProject = projects[index];

                // 更新UI
                document.querySelectorAll('.project-item').forEach(item => {
                    item.classList.remove('selected');
                });
                document.querySelector(\`[data-index="\${index}"]\`).classList.add('selected');

                // 显示项目信息
                document.getElementById('selected-project-name').textContent = selectedProject.name;
                document.getElementById('selected-project-path').textContent = selectedProject.path;
                document.getElementById('selected-project-type').textContent = selectedProject.type;
                document.getElementById('selected-project-info').style.display = 'block';

                updateStepButtons();
            }

            function nextStep() {
                if (currentStep < steps.length - 1) {
                    currentStep++;
                    showStep(currentStep);
                    updateStepButtons();
                }
            }

            function previousStep() {
                if (currentStep > 0) {
                    currentStep--;
                    showStep(currentStep);
                    updateStepButtons();
                }
            }

            function showStep(step) {
                // 更新标签页
                document.querySelectorAll('.tab').forEach((tab, index) => {
                    if (index === step) {
                        tab.classList.add('active');
                    } else {
                        tab.classList.remove('active');
                    }
                });

                // 更新内容
                document.querySelectorAll('.tab-content').forEach((content, index) => {
                    if (index === step) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
            }

            function updateStepButtons() {
                const prevBtn = document.getElementById('prev-btn');
                const nextBtn = document.getElementById('next-btn');
                const deployBtn = document.getElementById('deploy-btn');
                const redeployBtn = document.getElementById('redeploy-btn');

                // 上一步按钮
                if (currentStep > 0 && !isDeploying && !isDeployed) {
                    prevBtn.style.display = 'inline-block';
                } else {
                    prevBtn.style.display = 'none';
                }

                // 下一步/部署按钮
                if (currentStep === steps.length - 1) {
                    nextBtn.style.display = 'none';

                    // 根据部署状态设置按钮状态
                    if (isDeploying) {
                        deployBtn.style.display = 'inline-block';
                        deployBtn.disabled = true;
                        deployBtn.textContent = '部署中...';
                        redeployBtn.style.display = 'none';
                    } else if (isDeployed) {
                        deployBtn.style.display = 'none';
                        redeployBtn.style.display = 'inline-block';
                        redeployBtn.disabled = false;
                        redeployBtn.textContent = '重新部署';
                    } else {
                        deployBtn.style.display = 'inline-block';
                        deployBtn.disabled = false;
                        deployBtn.textContent = '开始部署';
                        deployBtn.style.backgroundColor = '';
                        deployBtn.style.color = '';
                        redeployBtn.style.display = 'none';
                    }
                } else {
                    nextBtn.style.display = 'inline-block';
                    deployBtn.style.display = 'none';
                    redeployBtn.style.display = 'none';

                    // 检查是否可以进入下一步
                    if (currentStep === 0) {
                        nextBtn.disabled = !selectedProject;
                    } else {
                        nextBtn.disabled = false;
                    }
                }
            }

            function startDeploy() {
                if (!selectedProject) {
                    showAlert('请先选择一个项目', 'error');
                    return;
                }

                if (isDeploying) {
                    showAlert('部署正在进行中，请稍候...', 'warning');
                    return;
                }

                if (isDeployed) {
                    showAlert('项目已经部署，请勿重复部署', 'warning');
                    return;
                }

                const contextPath = document.getElementById('contextPath').value === 'custom'
                    ? document.getElementById('customPath').value
                    : document.getElementById('contextPath').value;

                // 设置部署状态
                isDeploying = true;
                isDeployed = false;
                updateStepButtons();

                const deployData = {
                    instanceId: '${instanceData?.id || ""}',
                    projectName: selectedProject.name,
                    projectPath: selectedProject.path,
                    projectType: selectedProject.type,
                    contextPath: contextPath,
                    enableHotDeploy: document.getElementById('enableHotDeploy').checked,
                    hotDeployConfig: {
                        watchExtensions: document.getElementById('watchExtensions').value.split(',').map(s => s.trim()),
                        excludeDirectories: document.getElementById('excludeDirectories').value.split(',').map(s => s.trim()),
                        deployDelay: parseInt(document.getElementById('deployDelay').value),
                        restartApp: document.getElementById('restartApp').checked
                    }
                };

                showLoading();
                document.getElementById('loading-text').textContent = '正在部署项目...';
                document.getElementById('deploy-log').style.display = 'block';

                sendMessage('deployProject', deployData);
            }

            function redeploy() {
                if (!selectedProject) {
                    showAlert('请先选择一个项目', 'error');
                    return;
                }

                // 使用自定义确认对话框
                showConfirm(
                    '确定要重新部署项目吗？这将覆盖之前的部署。',
                    function() {
                        // 用户确认，执行重新部署
                        // 重置部署状态
                        isDeploying = false;
                        isDeployed = false;

                        // 清空之前的状态显示
                        document.getElementById('deploy-status').innerHTML =
                            '<div style="text-align: center; padding: 20px; color: var(--vscode-descriptionForeground);">准备重新部署...</div>';

                        // 调用部署函数
                        startDeploy();
                    },
                    function() {
                        // 用户取消，不执行任何操作
                        console.log('User cancelled redeploy');
                    }
                );
            }

            function handleMessage(message) {
                switch (message.command) {
                    case 'projectsScanned':
                        hideLoading();
                        projects = message.data.projects;
                        renderProjectList(projects);
                        break;
                    case 'projectDeployed':
                        hideLoading();
                        isDeploying = false;

                        if (message.data.success) {
                            isDeployed = true;
                            document.getElementById('deploy-status').innerHTML =
                                '<div class="alert alert-success">项目部署成功！<br><small>项目已部署到Tomcat实例，请勿重复部署。</small></div>';
                            if (message.data.result.buildOutput) {
                                document.getElementById('log-content').value = message.data.result.buildOutput;
                            }
                        } else {
                            isDeployed = false;

                            // 获取错误信息
                            const errorMessage = message.data.error || message.data.result?.message || '未知错误';
                            const buildOutput = message.data.result?.buildOutput || '';

                            // 创建详细的错误显示
                            let errorHtml = '<div class="alert alert-error">';
                            errorHtml += '<div style="font-weight: bold; margin-bottom: 10px;">部署失败</div>';

                            // 显示主要错误信息
                            if (errorMessage.includes('Build failed with exit code')) {
                                errorHtml += '<div style="margin-bottom: 10px;">构建失败，请检查以下信息：</div>';
                            }

                            // 显示错误详情（可折叠）
                            errorHtml += '<details style="margin-top: 10px;">';
                            errorHtml += '<summary style="cursor: pointer; font-weight: bold;">点击查看详细错误信息</summary>';
                            errorHtml += '<pre style="background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; overflow-x: auto; white-space: pre-wrap; font-size: 12px;">';
                            errorHtml += escapeHtml(errorMessage);
                            errorHtml += '</pre>';
                            errorHtml += '</details>';

                            // 如果有构建输出，也显示出来
                            if (buildOutput.trim()) {
                                errorHtml += '<details style="margin-top: 10px;">';
                                errorHtml += '<summary style="cursor: pointer; font-weight: bold;">点击查看构建日志</summary>';
                                errorHtml += '<pre style="background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; overflow-x: auto; white-space: pre-wrap; font-size: 12px;">';
                                errorHtml += escapeHtml(buildOutput);
                                errorHtml += '</pre>';
                                errorHtml += '</details>';
                            }

                            errorHtml += '</div>';

                            document.getElementById('deploy-status').innerHTML = errorHtml;

                            // 同时更新日志区域
                            if (buildOutput.trim()) {
                                document.getElementById('log-content').value = buildOutput;
                            }
                        }

                        // 更新按钮状态
                        updateStepButtons();
                        break;
                }
            }

            function renderProjectList(projects) {
                const listContainer = document.getElementById('project-list');

                if (projects.length === 0) {
                    listContainer.innerHTML =
                        '<div style="text-align: center; padding: 40px; color: var(--vscode-descriptionForeground);">' +
                        '未找到WAR包装类型的Java Web项目<br>' +
                        '<small style="opacity: 0.8; margin-top: 10px; display: block;">' +
                        '请确保项目使用 &lt;packaging&gt;war&lt;/packaging&gt; 或应用了war插件' +
                        '</small></div>';
                    return;
                }

                const html = projects.map((project, index) => \`
                    <div class="project-item" data-index="\${index}" onclick="selectProject(\${index})">
                        <div class="project-name">\${project.name}</div>
                        <div class="project-path">\${project.path}</div>
                        <div class="project-type">\${project.type}</div>
                    </div>
                \`).join('');

                listContainer.innerHTML = html;
            }
        `;

    return this.getBaseTemplate("部署项目", content, scripts);
  }
}
