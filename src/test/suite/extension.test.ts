import * as assert from 'assert';
import * as vscode from 'vscode';
import { TomcatInstance, TomcatInstanceStatus, BrowserType } from '../../models/TomcatInstance';
import { ProjectConfiguration, ProjectType } from '../../models/ProjectConfiguration';
import { PortManager } from '../../models/PortManager';
import { TomcatInstanceManager } from '../../services/TomcatInstanceManager';
import { ProjectDeploymentService } from '../../services/ProjectDeploymentService';
import { BrowserService } from '../../services/BrowserService';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    suite('TomcatInstance Tests', () => {
        test('Create TomcatInstance', () => {
            const config = {
                id: 'test-instance',
                name: 'Test Instance',
                description: 'Test Description',
                baseTomcatPath: '/usr/local/tomcat',
                instancePath: '/tmp/test-instance',
                ports: {
                    httpPort: 8080,
                    httpsPort: 8443,
                    ajpPort: 8009,
                    jmxPort: 9999,
                    shutdownPort: 8005
                },
                jvm: {
                    jrePath: '/usr/lib/jvm/java-11-openjdk',
                    minHeapSize: '256m',
                    maxHeapSize: '512m',
                    additionalArgs: []
                },
                browser: {
                    type: BrowserType.DEFAULT,
                    autoOpen: true,
                    defaultPage: ''
                },
                deployedApps: [],
                status: TomcatInstanceStatus.STOPPED,
                createdAt: new Date(),
                lastModified: new Date(),
                hotDeployEnabled: true,
                logPath: '/tmp/test-instance/logs'
            };

            const instance = new TomcatInstance(config);
            
            assert.strictEqual(instance.getId(), 'test-instance');
            assert.strictEqual(instance.getName(), 'Test Instance');
            assert.strictEqual(instance.getStatus(), TomcatInstanceStatus.STOPPED);
            assert.strictEqual(instance.getHttpUrl(), 'http://localhost:8080/');
            assert.strictEqual(instance.getHttpUrl('/test'), 'http://localhost:8080/test');
        });

        test('Validate TomcatInstance Configuration', () => {
            const config = {
                id: 'test-instance',
                name: '', // Invalid: empty name
                description: '',
                baseTomcatPath: '', // Invalid: empty path
                instancePath: '/tmp/test-instance',
                ports: {
                    httpPort: 8080,
                    httpsPort: 8080, // Invalid: duplicate port
                    ajpPort: 8009,
                    jmxPort: 9999,
                    shutdownPort: 8005
                },
                jvm: {
                    jrePath: '', // Invalid: empty JRE path
                    minHeapSize: '256m',
                    maxHeapSize: '512m',
                    additionalArgs: []
                },
                browser: {
                    type: BrowserType.DEFAULT,
                    autoOpen: true,
                    defaultPage: ''
                },
                deployedApps: [],
                status: TomcatInstanceStatus.STOPPED,
                createdAt: new Date(),
                lastModified: new Date(),
                hotDeployEnabled: true,
                logPath: '/tmp/test-instance/logs'
            };

            const instance = new TomcatInstance(config);
            const errors = instance.validateConfiguration();
            
            assert.ok(errors.length > 0, 'Should have validation errors');
            assert.ok(errors.some(error => error.includes('name')), 'Should have name error');
            assert.ok(errors.some(error => error.includes('Tomcat path')), 'Should have path error');
            assert.ok(errors.some(error => error.includes('JRE path')), 'Should have JRE error');
            assert.ok(errors.some(error => error.includes('Duplicate ports')), 'Should have duplicate ports error');
        });
    });

    suite('ProjectConfiguration Tests', () => {
        test('Create ProjectConfiguration', () => {
            const projectConfig = ProjectConfiguration.createDefault('/path/to/project', 'TestProject');
            
            assert.strictEqual(projectConfig.getName(), 'TestProject');
            assert.strictEqual(projectConfig.getProjectPath(), '/path/to/project');
            assert.strictEqual(projectConfig.getContextPath(), 'ROOT');
            assert.strictEqual(projectConfig.isHotDeployEnabled(), true);
            assert.strictEqual(projectConfig.isAutoBuildEnabled(), true);
        });

        test('Validate ProjectConfiguration', () => {
            const projectConfig = ProjectConfiguration.createDefault('/path/to/project', 'TestProject');
            
            // Test valid configuration
            let errors = projectConfig.validateConfiguration();
            assert.strictEqual(errors.length, 0, 'Valid configuration should have no errors');
            
            // Test invalid configuration
            projectConfig.updateConfiguration({
                name: '', // Invalid: empty name
                contextPath: 'invalid-path', // Invalid: doesn't start with /
                build: {
                    ...projectConfig.getConfiguration().build,
                    buildCommand: '', // Invalid: empty build command
                    deployDelay: -1 // Invalid: negative delay
                }
            } as any);
            
            errors = projectConfig.validateConfiguration();
            assert.ok(errors.length > 0, 'Invalid configuration should have errors');
        });

        test('File watching logic', () => {
            const projectConfig = ProjectConfiguration.createDefault('/path/to/project', 'TestProject');
            
            // Test file watching
            assert.strictEqual(projectConfig.shouldWatchFile('/path/to/project/src/main/java/Test.java'), true);
            assert.strictEqual(projectConfig.shouldWatchFile('/path/to/project/src/main/webapp/index.jsp'), true);
            assert.strictEqual(projectConfig.shouldWatchFile('/path/to/project/target/classes/Test.class'), false);
            assert.strictEqual(projectConfig.shouldWatchFile('/path/to/project/node_modules/package.json'), false);
        });
    });

    suite('PortManager Tests', () => {
        test('Port availability check', async () => {
            const portManager = PortManager.getInstance();
            
            // Test port availability (assuming these ports are not in use)
            const isAvailable = await portManager.isPortAvailable(65432);
            assert.strictEqual(typeof isAvailable, 'boolean');
        });

        test('Port reservation', async () => {
            const portManager = PortManager.getInstance();
            const testInstanceId = 'test-instance-' + Date.now();
            
            // Reserve a port
            const reserved = portManager.reservePort(65433, testInstanceId);
            assert.strictEqual(reserved, true, 'Should be able to reserve available port');
            
            // Try to reserve the same port again
            const reservedAgain = portManager.reservePort(65433, 'another-instance');
            assert.strictEqual(reservedAgain, false, 'Should not be able to reserve already reserved port');
            
            // Release the port
            portManager.releasePort(65433);
            
            // Should be able to reserve again
            const reservedAfterRelease = portManager.reservePort(65433, 'another-instance');
            assert.strictEqual(reservedAfterRelease, true, 'Should be able to reserve after release');
            
            // Cleanup
            portManager.releasePort(65433);
        });

        test('Generate available port configuration', async () => {
            const portManager = PortManager.getInstance();
            
            const portConfig = await portManager.generateAvailablePortConfiguration();
            assert.ok(portConfig, 'Should generate port configuration');
            
            if (portConfig) {
                assert.ok(portConfig.httpPort >= 1024 && portConfig.httpPort <= 65535);
                assert.ok(portConfig.httpsPort >= 1024 && portConfig.httpsPort <= 65535);
                assert.ok(portConfig.ajpPort >= 1024 && portConfig.ajpPort <= 65535);
                assert.ok(portConfig.jmxPort >= 1024 && portConfig.jmxPort <= 65535);
                assert.ok(portConfig.shutdownPort >= 1024 && portConfig.shutdownPort <= 65535);
                
                // All ports should be different
                const ports = [
                    portConfig.httpPort,
                    portConfig.httpsPort,
                    portConfig.ajpPort,
                    portConfig.jmxPort,
                    portConfig.shutdownPort
                ];
                const uniquePorts = new Set(ports);
                assert.strictEqual(uniquePorts.size, ports.length, 'All ports should be unique');
            }
        });
    });

    suite('BrowserService Tests', () => {
        test('Browser detection', async () => {
            const browserService = BrowserService.getInstance();
            
            const availableBrowsers = browserService.getAvailableBrowsers();
            assert.ok(Array.isArray(availableBrowsers), 'Should return array of browsers');
            
            // Should always have default browser
            const defaultBrowser = availableBrowsers.find(b => b.type === BrowserType.DEFAULT);
            assert.ok(defaultBrowser, 'Should have default browser');
            assert.strictEqual(defaultBrowser.isAvailable, true, 'Default browser should be available');
        });

        test('Custom browser path validation', () => {
            const browserService = BrowserService.getInstance();
            
            // Test invalid paths
            assert.strictEqual(browserService.validateCustomBrowserPath(''), false);
            assert.strictEqual(browserService.validateCustomBrowserPath('/nonexistent/path'), false);
            
            // Test valid path (assuming this exists on most systems)
            if (process.platform === 'win32') {
                // On Windows, test a common system file
                assert.strictEqual(browserService.validateCustomBrowserPath('C:\\Windows\\System32\\notepad.exe'), true);
            } else {
                // On Unix-like systems, test a common binary
                assert.strictEqual(browserService.validateCustomBrowserPath('/bin/sh'), true);
            }
        });
    });

    suite('ProjectDeploymentService Tests', () => {
        test('Project type detection', () => {
            const deploymentService = ProjectDeploymentService.getInstance();
            
            // Mock project directories for testing
            // In a real test, you would create temporary directories with the appropriate files
            
            // Test Maven project detection
            // This would require creating a temporary directory with pom.xml
            
            // Test Gradle project detection
            // This would require creating a temporary directory with build.gradle
            
            // For now, just test that the method exists and returns a valid enum value
            const projectType = deploymentService.detectProjectType('/nonexistent/path');
            assert.ok(Object.values(ProjectType).includes(projectType), 'Should return valid project type');
        });
    });

    suite('Integration Tests', () => {
        test('Create instance and deploy project workflow', async () => {
            // This is a mock integration test
            // In a real scenario, you would:
            // 1. Create a temporary Tomcat installation
            // 2. Create a test project
            // 3. Create a Tomcat instance
            // 4. Deploy the project
            // 5. Verify deployment
            // 6. Clean up
            
            // For now, just test that the services can be instantiated
            const instanceManager = TomcatInstanceManager.getInstance();
            const deploymentService = ProjectDeploymentService.getInstance();
            
            assert.ok(instanceManager, 'Instance manager should be available');
            assert.ok(deploymentService, 'Deployment service should be available');
            
            // Test that we can get empty lists initially
            const instances = instanceManager.getAllInstances();
            assert.ok(Array.isArray(instances), 'Should return array of instances');
        });
    });
});
