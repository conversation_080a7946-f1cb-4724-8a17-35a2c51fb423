/**
 * Tomcat实例状态枚举
 */
export enum TomcatInstanceStatus {
    STOPPED = 'stopped',
    STARTING = 'starting',
    RUNNING = 'running',
    STOPPING = 'stopping',
    ERROR = 'error'
}

/**
 * 浏览器类型枚举
 */
export enum BrowserType {
    DEFAULT = 'default',
    CHROME = 'chrome',
    FIREFOX = 'firefox',
    SAFARI = 'safari',
    EDGE = 'edge',
    CUSTOM = 'custom'
}

/**
 * 端口配置接口
 */
export interface PortConfiguration {
    /** HTTP端口 */
    httpPort: number;
    /** HTTPS端口 */
    httpsPort: number;
    /** AJP端口 */
    ajpPort: number;
    /** JMX端口 */
    jmxPort: number;
    /** 管理端口（shutdown端口） */
    shutdownPort: number;
}

/**
 * 浏览器配置接口
 */
export interface BrowserConfiguration {
    /** 浏览器类型 */
    type: BrowserType;
    /** 自定义浏览器路径 */
    customPath?: string;
    /** 是否自动打开浏览器 */
    autoOpen: boolean;
    /** 默认启动页面 */
    defaultPage: string;
}

/**
 * JVM配置接口
 */
export interface JvmConfiguration {
    /** JRE路径 */
    jrePath: string;
    /** 最小堆内存 */
    minHeapSize: string;
    /** 最大堆内存 */
    maxHeapSize: string;
    /** 其他JVM参数 */
    additionalArgs: string[];
}

/**
 * 部署的应用信息
 */
export interface DeployedApplication {
    /** 应用ID */
    id: string;
    /** 应用名称 */
    name: string;
    /** 上下文路径 */
    contextPath: string;
    /** WAR文件路径 */
    warPath: string;
    /** 项目源路径 */
    sourcePath: string;
    /** 部署状态 */
    status: 'deployed' | 'undeployed' | 'failed';
    /** 部署时间 */
    deployedAt?: Date;
    /** 访问URL */
    url: string;
}

/**
 * Tomcat实例配置
 */
export interface TomcatInstanceConfiguration {
    /** 实例ID */
    id: string;
    /** 实例名称 */
    name: string;
    /** 实例描述 */
    description?: string;
    /** 基础Tomcat路径 */
    baseTomcatPath: string;
    /** 实例工作目录 */
    instancePath: string;
    /** 端口配置 */
    ports: PortConfiguration;
    /** JVM配置 */
    jvm: JvmConfiguration;
    /** 浏览器配置 */
    browser: BrowserConfiguration;
    /** 部署的应用列表 */
    deployedApps: DeployedApplication[];
    /** 实例状态 */
    status: TomcatInstanceStatus;
    /** 进程ID */
    processId?: number;
    /** 创建时间 */
    createdAt: Date;
    /** 最后修改时间 */
    lastModified: Date;
    /** 是否启用热部署 */
    hotDeployEnabled: boolean;
    /** 日志文件路径 */
    logPath: string;
}

/**
 * Tomcat实例类
 */
export class TomcatInstance {
    private config: TomcatInstanceConfiguration;

    constructor(config: TomcatInstanceConfiguration) {
        this.config = { ...config };
    }

    /**
     * 获取实例配置
     */
    getConfiguration(): TomcatInstanceConfiguration {
        return { ...this.config };
    }

    /**
     * 更新实例配置
     */
    updateConfiguration(updates: Partial<TomcatInstanceConfiguration>): void {
        this.config = {
            ...this.config,
            ...updates,
            lastModified: new Date()
        };
    }

    /**
     * 获取实例ID
     */
    getId(): string {
        return this.config.id;
    }

    /**
     * 获取实例名称
     */
    getName(): string {
        return this.config.name;
    }

    /**
     * 获取实例状态
     */
    getStatus(): TomcatInstanceStatus {
        return this.config.status;
    }

    /**
     * 设置实例状态
     */
    setStatus(status: TomcatInstanceStatus): void {
        this.config.status = status;
        this.config.lastModified = new Date();
    }

    /**
     * 获取HTTP访问URL
     */
    getHttpUrl(contextPath: string = ''): string {
        const port = this.config.ports.httpPort;
        const path = contextPath.startsWith('/') ? contextPath : `/${contextPath}`;
        return `http://localhost:${port}${path}`;
    }

    /**
     * 获取HTTPS访问URL
     */
    getHttpsUrl(contextPath: string = ''): string {
        const port = this.config.ports.httpsPort;
        const path = contextPath.startsWith('/') ? contextPath : `/${contextPath}`;
        return `https://localhost:${port}${path}`;
    }

    /**
     * 添加部署的应用
     */
    addDeployedApp(app: DeployedApplication): void {
        const existingIndex = this.config.deployedApps.findIndex(a => a.id === app.id);
        if (existingIndex >= 0) {
            this.config.deployedApps[existingIndex] = app;
        } else {
            this.config.deployedApps.push(app);
        }
        this.config.lastModified = new Date();
    }

    /**
     * 移除部署的应用
     */
    removeDeployedApp(appId: string): void {
        this.config.deployedApps = this.config.deployedApps.filter(app => app.id !== appId);
        this.config.lastModified = new Date();
    }

    /**
     * 获取部署的应用列表
     */
    getDeployedApps(): DeployedApplication[] {
        return [...this.config.deployedApps];
    }

    /**
     * 检查端口是否被使用
     */
    isPortInUse(port: number): boolean {
        const ports = this.config.ports;
        return port === ports.httpPort || 
               port === ports.httpsPort || 
               port === ports.ajpPort || 
               port === ports.jmxPort || 
               port === ports.shutdownPort;
    }

    /**
     * 获取所有使用的端口
     */
    getAllPorts(): number[] {
        const ports = this.config.ports;
        return [ports.httpPort, ports.httpsPort, ports.ajpPort, ports.jmxPort, ports.shutdownPort];
    }

    /**
     * 验证配置是否有效
     */
    validateConfiguration(): string[] {
        const errors: string[] = [];
        
        if (!this.config.name.trim()) {
            errors.push('Instance name is required');
        }
        
        if (!this.config.baseTomcatPath.trim()) {
            errors.push('Base Tomcat path is required');
        }
        
        if (!this.config.jvm.jrePath.trim()) {
            errors.push('JRE path is required');
        }
        
        // 检查端口范围
        const ports = this.getAllPorts();
        for (const port of ports) {
            if (port < 1024 || port > 65535) {
                errors.push(`Invalid port: ${port}. Port must be between 1024 and 65535`);
            }
        }
        
        // 检查端口重复
        const uniquePorts = new Set(ports);
        if (uniquePorts.size !== ports.length) {
            errors.push('Duplicate ports detected');
        }
        
        return errors;
    }

    /**
     * 转换为JSON
     */
    toJSON(): TomcatInstanceConfiguration {
        return this.getConfiguration();
    }

    /**
     * 从JSON创建实例
     */
    static fromJSON(json: TomcatInstanceConfiguration): TomcatInstance {
        return new TomcatInstance(json);
    }
}
