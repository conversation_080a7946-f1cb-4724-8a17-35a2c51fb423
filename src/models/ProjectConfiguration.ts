/**
 * 项目类型枚举
 */
export enum ProjectType {
  MAVEN = "maven",
  GRADLE = "gradle",
  PLAIN_JAVA = "plain-java",
}

/**
 * 构建配置接口
 */
export interface BuildConfiguration {
  /** 项目类型 */
  type: ProjectType;
  /** 构建命令 */
  buildCommand: string;
  /** 输出目录 */
  outputDirectory: string;
  /** WAR文件名 */
  warFileName: string;
  /** 是否自动构建 */
  autoBuild: boolean;
}

/**
 * 热部署配置接口
 */
export interface HotDeployConfiguration {
  /** 是否启用热部署 */
  enabled: boolean;
  /** 监听的文件扩展名 */
  watchExtensions: string[];
  /** 排除的目录 */
  excludeDirectories: string[];
  /** 部署延迟（毫秒） */
  deployDelay: number;
  /** 是否重启应用 */
  restartApp: boolean;
}

/**
 * 项目配置接口
 */
export interface ProjectConfigurationData {
  /** 项目ID */
  id: string;
  /** 项目名称 */
  name: string;
  /** 项目路径 */
  projectPath: string;
  /** 上下文路径 */
  contextPath: string;
  /** 关联的Tomcat实例ID */
  tomcatInstanceId?: string;
  /** 构建配置 */
  build: BuildConfiguration;
  /** 热部署配置 */
  hotDeploy: HotDeployConfiguration;
  /** 环境变量 */
  environmentVariables: { [key: string]: string };
  /** JVM参数 */
  jvmArgs: string[];
  /** 创建时间 */
  createdAt: Date;
  /** 最后修改时间 */
  lastModified: Date;
  /** 最后部署时间 */
  lastDeployed?: Date;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 项目配置类
 */
export class ProjectConfiguration {
  private config: ProjectConfigurationData;

  constructor(config: ProjectConfigurationData) {
    this.config = { ...config };
  }

  /**
   * 获取项目配置
   */
  getConfiguration(): ProjectConfigurationData {
    return { ...this.config };
  }

  /**
   * 更新项目配置
   */
  updateConfiguration(updates: Partial<ProjectConfigurationData>): void {
    this.config = {
      ...this.config,
      ...updates,
      lastModified: new Date(),
    };
  }

  /**
   * 获取项目ID
   */
  getId(): string {
    return this.config.id;
  }

  /**
   * 获取项目名称
   */
  getName(): string {
    return this.config.name;
  }

  /**
   * 获取项目路径
   */
  getProjectPath(): string {
    return this.config.projectPath;
  }

  /**
   * 获取上下文路径
   */
  getContextPath(): string {
    return this.config.contextPath;
  }

  /**
   * 设置上下文路径
   */
  setContextPath(contextPath: string): void {
    this.config.contextPath = contextPath;
    this.config.lastModified = new Date();
  }

  /**
   * 获取关联的Tomcat实例ID
   */
  getTomcatInstanceId(): string | undefined {
    return this.config.tomcatInstanceId;
  }

  /**
   * 设置关联的Tomcat实例ID
   */
  setTomcatInstanceId(instanceId: string): void {
    this.config.tomcatInstanceId = instanceId;
    this.config.lastModified = new Date();
  }

  /**
   * 获取WAR文件路径
   */
  getWarFilePath(): string {
    const build = this.config.build;
    return `${this.config.projectPath}/${build.outputDirectory}/${build.warFileName}`;
  }

  /**
   * 获取构建命令
   */
  getBuildCommand(): string {
    return this.config.build.buildCommand;
  }

  /**
   * 是否启用热部署
   */
  isHotDeployEnabled(): boolean {
    return this.config.hotDeploy.enabled;
  }

  /**
   * 是否启用自动构建
   */
  isAutoBuildEnabled(): boolean {
    return this.config.build.autoBuild;
  }

  /**
   * 获取监听的文件扩展名
   */
  getWatchExtensions(): string[] {
    return [...this.config.hotDeploy.watchExtensions];
  }

  /**
   * 获取排除的目录
   */
  getExcludeDirectories(): string[] {
    return [...this.config.hotDeploy.excludeDirectories];
  }

  /**
   * 获取部署延迟
   */
  getDeployDelay(): number {
    return this.config.hotDeploy.deployDelay;
  }

  /**
   * 是否需要重启应用
   */
  shouldRestartApp(): boolean {
    return this.config.hotDeploy.restartApp;
  }

  /**
   * 更新最后部署时间
   */
  updateLastDeployed(): void {
    this.config.lastDeployed = new Date();
    this.config.lastModified = new Date();
  }

  /**
   * 检查文件是否需要监听
   */
  shouldWatchFile(filePath: string): boolean {
    if (!this.config.hotDeploy.enabled) {
      return false;
    }

    // 检查是否在排除目录中
    const relativePath = filePath.replace(this.config.projectPath, "");
    for (const excludeDir of this.config.hotDeploy.excludeDirectories) {
      if (relativePath.startsWith(excludeDir)) {
        return false;
      }
    }

    // 检查文件扩展名
    const extension = filePath.split(".").pop()?.toLowerCase();
    return extension
      ? this.config.hotDeploy.watchExtensions.includes(extension)
      : false;
  }

  /**
   * 验证配置是否有效
   */
  validateConfiguration(): string[] {
    const errors: string[] = [];

    if (!this.config.name.trim()) {
      errors.push("Project name is required");
    }

    if (!this.config.projectPath.trim()) {
      errors.push("Project path is required");
    }

    if (!this.config.contextPath.trim()) {
      errors.push("Context path is required");
    }

    // 验证上下文路径格式
    const contextPath = this.config.contextPath;
    if (contextPath !== "ROOT" && !contextPath.startsWith("/")) {
      errors.push('Context path must start with "/" or be "ROOT"');
    }

    if (!this.config.build.buildCommand.trim()) {
      errors.push("Build command is required");
    }

    if (!this.config.build.outputDirectory.trim()) {
      errors.push("Output directory is required");
    }

    if (!this.config.build.warFileName.trim()) {
      errors.push("WAR file name is required");
    }

    if (this.config.hotDeploy.deployDelay < 0) {
      errors.push("Deploy delay must be non-negative");
    }

    return errors;
  }

  /**
   * 转换为JSON
   */
  toJSON(): ProjectConfigurationData {
    return this.getConfiguration();
  }

  /**
   * 从JSON创建实例
   */
  static fromJSON(json: ProjectConfigurationData): ProjectConfiguration {
    return new ProjectConfiguration(json);
  }

  /**
   * 创建默认配置
   */
  static createDefault(
    projectPath: string,
    projectName: string
  ): ProjectConfiguration {
    const config: ProjectConfigurationData = {
      id: generateId(),
      name: projectName,
      projectPath,
      contextPath: "ROOT",
      build: {
        type: ProjectType.MAVEN,
        buildCommand: "mvn clean package",
        outputDirectory: "target",
        warFileName: `${projectName}.war`,
        autoBuild: true,
      },
      hotDeploy: {
        enabled: true,
        watchExtensions: ["java", "jsp", "html", "css", "js", "xml"],
        excludeDirectories: ["target", "node_modules", ".git"],
        deployDelay: 1000,
        restartApp: false,
      },
      environmentVariables: {},
      jvmArgs: [],
      createdAt: new Date(),
      lastModified: new Date(),
      enabled: true,
    };

    return new ProjectConfiguration(config);
  }

  /**
   * 使用指定ID创建配置
   */
  static createWithId(
    id: string,
    projectPath: string,
    projectName: string
  ): ProjectConfiguration {
    const config: ProjectConfigurationData = {
      id: id, // 使用指定的ID
      name: projectName,
      projectPath,
      contextPath: "ROOT",
      build: {
        type: ProjectType.MAVEN,
        buildCommand: "mvn clean package",
        outputDirectory: "target",
        warFileName: `${projectName}.war`,
        autoBuild: true,
      },
      hotDeploy: {
        enabled: true,
        watchExtensions: ["java", "jsp", "html", "css", "js", "xml"],
        excludeDirectories: ["target", "node_modules", ".git"],
        deployDelay: 1000,
        restartApp: false,
      },
      environmentVariables: {},
      jvmArgs: [],
      createdAt: new Date(),
      lastModified: new Date(),
      enabled: true,
    };

    return new ProjectConfiguration(config);
  }
}

/**
 * 生成唯一ID
 */
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
