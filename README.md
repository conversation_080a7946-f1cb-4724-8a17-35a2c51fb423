# VSCode Tomcat Manager

A powerful VSCode extension for managing multiple Tomcat instances with hot deployment capabilities, similar to IntelliJ IDEA's Tomcat and TomEE plugin.

## Features

### 🚀 Multi-Instance Management

- Create multiple Tomcat instances from a single base installation
- Independent configuration for each instance
- Start, stop, and restart instances individually or in batch
- Real-time status monitoring

### 🔧 Advanced Port Configuration

- Automatic port conflict detection
- Configure HTTP, HTTPS, AJP, JMX, and management ports
- Smart port allocation for new instances
- Port usage monitoring and reporting

### 📦 Project Deployment

- Support for Maven, Gradle, and plain Java web projects
- Configurable context paths (including ROOT deployment)
- One-click deployment with automatic building
- Deploy multiple projects to the same instance

### 🔥 Hot Deployment

- Real-time file watching and automatic redeployment
- Configurable file extensions and exclusion patterns
- Incremental updates for static files
- Smart deployment delays to batch changes

### 🌐 Browser Integration

- Support for multiple browsers (Chrome, Firefox, Safari, Edge)
- Automatic browser launch on startup
- Configurable startup pages
- Custom browser path support

### ⚙️ JRE Management

- Different JRE versions for different instances
- Automatic JRE detection
- Custom JVM parameters per instance
- Memory configuration (heap size, etc.)

## Installation

1. Open VSCode
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Tomcat Manager"
4. Click Install

## Quick Start

### 1. Configure Base Tomcat

1. Open VSCode settings (Ctrl+,)
2. Search for "Tomcat Manager"
3. Set the "Base Tomcat Path" to your Tomcat installation directory

### 2. Create Your First Instance

1. Open the Tomcat Manager view in the Explorer panel
2. Click the "+" button to add a new instance
3. Follow the wizard to configure your instance
4. Click "Start" to launch your Tomcat instance

### 3. Deploy a Project

1. Right-click on a running Tomcat instance
2. Select "Deploy Project"
3. Choose your Java web project
4. Configure the context path
5. Your project will be built and deployed automatically

## Configuration

### Global Settings

```json
{
  "tomcatManager.baseTomcatPath": "/usr/local/tomcat",
  "tomcatManager.defaultJrePath": "/usr/lib/jvm/java-11-openjdk",
  "tomcatManager.defaultBrowser": "chrome",
  "tomcatManager.autoOpenBrowser": true,
  "tomcatManager.portRange": {
    "httpStart": 8080,
    "httpsStart": 8443,
    "ajpStart": 8009,
    "jmxStart": 9999,
    "shutdownStart": 8005
  }
}
```

### Instance Configuration

Each Tomcat instance can be configured with:

- **Name and Description**: Identify your instances
- **Ports**: HTTP, HTTPS, AJP, JMX, and shutdown ports
- **JVM Settings**: JRE path, heap size, additional arguments
- **Browser Settings**: Auto-open, browser type, startup page
- **Hot Deploy**: Enable/disable, file patterns, deployment delay

### Project Configuration

Projects support:

- **Build Configuration**: Maven, Gradle, or custom build commands
- **Context Path**: ROOT or custom path (e.g., /myapp)
- **Hot Deploy**: File watching patterns and exclusions
- **Environment Variables**: Custom environment for deployment

## Commands

| Command                              | Description                     |
| ------------------------------------ | ------------------------------- |
| `Tomcat Manager: Add Instance`       | Create a new Tomcat instance    |
| `Tomcat Manager: Start Instance`     | Start a Tomcat instance         |
| `Tomcat Manager: Stop Instance`      | Stop a Tomcat instance          |
| `Tomcat Manager: Restart Instance`   | Restart a Tomcat instance       |
| `Tomcat Manager: Deploy Project`     | Deploy a project to an instance |
| `Tomcat Manager: Open in Browser`    | Open application in browser     |
| `Tomcat Manager: Show Logs`          | View Tomcat logs                |
| `Tomcat Manager: Configure Instance` | Modify instance settings        |

## Supported Project Types

### Maven Projects

- Automatic detection via `pom.xml`
- Default build command: `mvn clean package`
- Output directory: `target`

### Gradle Projects

- Automatic detection via `build.gradle` or `build.gradle.kts`
- Default build command: `./gradlew build`
- Output directory: `build/libs`

### Plain Java Projects

- Manual configuration required
- Custom build commands supported
- Flexible output directory configuration

## Hot Deployment

The extension supports intelligent hot deployment:

### Watched File Types

- `.java` - Java source files
- `.jsp` - JavaServer Pages
- `.html`, `.css`, `.js` - Static web resources
- `.xml` - Configuration files
- `.properties` - Property files

### Deployment Strategies

- **Static Files**: Direct copy to deployment directory
- **Java Classes**: Hot class replacement (JRE dependent)
- **Configuration Files**: Full application restart

### Configuration

```json
{
  "hotDeploy": {
    "enabled": true,
    "watchExtensions": ["java", "jsp", "html", "css", "js", "xml"],
    "excludeDirectories": ["target", "node_modules", ".git"],
    "deployDelay": 1000,
    "restartApp": false
  }
}
```

## Browser Support

### Supported Browsers

- **System Default**: Uses OS default browser
- **Google Chrome**: Full feature support
- **Mozilla Firefox**: Full feature support
- **Microsoft Edge**: Full feature support
- **Safari**: macOS only
- **Custom**: Specify custom browser executable

### Browser Features

- Automatic launch on Tomcat startup
- Incognito/private mode support
- New window options
- Custom startup URLs

## Troubleshooting

### Common Issues

**Port Already in Use**

- Check the port usage in the status bar
- Use the port conflict detection feature
- Configure different ports for your instances

**Build Failures**

- Verify your build command is correct
- Check that required tools (Maven/Gradle) are installed
- Review build output in the deployment logs

**Hot Deploy Not Working**

- Ensure hot deploy is enabled in project settings
- Check file extension patterns
- Verify exclusion directories are correct

**Browser Not Opening**

- Check browser configuration in settings
- Verify browser path for custom browsers
- Ensure auto-open is enabled

### Debug Mode

Enable debug logging by setting:

```json
{
  "tomcatManager.debug": true
}
```

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Clone the repository
2. Run `npm install`
3. Open in VSCode
4. Press F5 to launch extension development host

### Running Tests

```bash
npm test
```

## 📚 Documentation

- [📖 Complete Documentation](docs/README.md) - Index of all documentation
- [📋 Usage Guide](docs/USAGE.md) - Detailed usage instructions
- [🎯 Graphical UI Guide](docs/GRAPHICAL_UI_GUIDE.md) - UI operation guide
- [🔧 Technical Documentation](docs/) - Feature implementation and bug fix documentation
- [📝 Changelog](docs/CHANGELOG.md) - Version update information

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

Contributions are welcome! Please refer to the [Project Structure Documentation](docs/PROJECT_STRUCTURE.md) to understand the code organization.

## Support

If you encounter any issues or have suggestions:

1. Check the relevant debugging documentation first
2. Look for related fixes in the [Changelog](docs/CHANGELOG.md)
3. Create an issue on GitHub

---

**Languages**: [English](README.md) | [中文](README_CN.md)

**Enjoy developing with VSCode Tomcat Manager!** 🚀
