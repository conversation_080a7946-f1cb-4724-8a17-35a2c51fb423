# ⚙️ JVM参数配置功能

## 🎯 用户需求

用户需要在创建Tomcat实例时配置JVM参数，例如：
- `-Dfile.encoding=UTF-8`
- `-Dprocess.profile=fstest`
- `-Dspring.profiles.active=fstest`
- `-Dlogback.configurationFile=/Users/<USER>/workspace/logback.xml`
- `--add-opens=java.base/java.lang.reflect=ALL-UNNAMED`
- 等各种系统属性和模块开放参数

## 🔧 解决方案

### 1. 添加JVM参数输入区域

#### HTML界面增强
```html
<div class="form-group">
    <label for="jvmArgs">JVM参数</label>
    <div style="display: flex; gap: 8px; margin-bottom: 8px;">
        <button type="button" class="button button-secondary" onclick="addCommonJvmArgs()">
            📋 常用参数
        </button>
        <button type="button" class="button button-secondary" onclick="addJava9PlusArgs()">
            ☕ Java 9+ 模块参数
        </button>
        <button type="button" class="button button-secondary" onclick="clearJvmArgs()">
            🗑️ 清空
        </button>
    </div>
    <textarea id="jvmArgs" name="jvmArgs" rows="8" placeholder="每行一个JVM参数..."></textarea>
    <div class="help-text">每行输入一个JVM参数，支持系统属性(-D)、模块开放(--add-opens)等参数。以#开头的行为注释。</div>
</div>
```

### 2. 智能参数处理

#### JavaScript参数解析
```javascript
function createInstance() {
    const formData = new FormData(document.getElementById('instance-form'));
    
    // 处理JVM参数
    const jvmArgsText = formData.get('jvmArgs') || '';
    const jvmArgs = jvmArgsText
        .split('\n')                    // 按行分割
        .map(arg => arg.trim())         // 去除空白
        .filter(arg => arg.length > 0 && !arg.startsWith('#')); // 过滤空行和注释
    
    const data = {
        // ... 其他配置
        jvm: {
            jrePath: formData.get('jrePath') || '',
            minHeapSize: formData.get('minHeapSize'),
            maxHeapSize: formData.get('maxHeapSize'),
            additionalArgs: jvmArgs  // ✅ 解析后的JVM参数数组
        },
        // ...
    };
}
```

### 3. 预设参数模板

#### 常用JVM参数模板
```javascript
function addCommonJvmArgs() {
    const commonArgs = [
        '# 常用JVM参数',
        '-Dfile.encoding=UTF-8',
        '-Duser.timezone=Asia/Shanghai',
        '-Djava.awt.headless=true',
        '-Djava.security.egd=file:/dev/./urandom',
        '-server'
    ].join('\n');
    
    // 添加到文本区域
    appendToJvmArgs(commonArgs);
}
```

#### Java 9+ 模块参数模板
```javascript
function addJava9PlusArgs() {
    const java9Args = [
        '# Java 9+ 模块系统参数',
        '--add-opens=java.base/java.lang.reflect=ALL-UNNAMED',
        '--add-opens=java.base/java.math=ALL-UNNAMED',
        '--add-opens=java.base/java.lang=ALL-UNNAMED',
        '--add-opens=java.base/java.io=ALL-UNNAMED',
        '--add-opens=java.base/java.util=ALL-UNNAMED',
        '--add-opens=java.base/java.util.concurrent=ALL-UNNAMED',
        '--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED',
        '--add-opens=java.base/java.lang.ref=ALL-UNNAMED',
        '--add-opens=java.base/sun.nio.fs=ALL-UNNAMED',
        '--add-opens=java.base/java.nio.file=ALL-UNNAMED',
        '--add-opens=java.base/java.net=ALL-UNNAMED'
    ].join('\n');
    
    appendToJvmArgs(java9Args);
}
```

### 4. 参数应用到CATALINA_OPTS

#### 环境变量构建
```typescript
private buildEnvironment(config: TomcatInstanceConfiguration): {
  [key: string]: string;
} {
  return {
    JAVA_HOME: this.getJavaHome(config.jvm.jrePath),
    CATALINA_HOME: config.baseTomcatPath,
    CATALINA_BASE: config.instancePath,
    CATALINA_OPTS: [
      `-Xms${config.jvm.minHeapSize}`,      // 最小堆内存
      `-Xmx${config.jvm.maxHeapSize}`,      // 最大堆内存
      ...config.jvm.additionalArgs,         // ✅ 用户配置的JVM参数
    ].join(" "),
  };
}
```

## ✅ 功能特性

### 1. 多行文本输入
- **大文本区域**：8行高度，便于输入多个参数
- **每行一个参数**：清晰的参数组织方式
- **注释支持**：以`#`开头的行会被忽略
- **自动过滤**：空行和注释行自动过滤

### 2. 快捷参数模板
- **📋 常用参数**：编码、时区、无头模式等常用设置
- **☕ Java 9+ 模块参数**：解决模块系统访问问题的--add-opens参数
- **🗑️ 清空**：一键清空所有参数

### 3. 智能参数处理
- **自动解析**：按行分割并去除空白
- **注释过滤**：忽略以#开头的注释行
- **数组存储**：转换为字符串数组存储在配置中

### 4. 实时应用
- **环境变量集成**：参数自动添加到CATALINA_OPTS
- **启动时生效**：Tomcat启动时自动应用所有JVM参数

## 🔄 使用流程

### 1. 创建实例时配置
1. 打开"创建Tomcat实例"界面
2. 在"JVM配置"部分找到"JVM参数"文本区域
3. 可以选择：
   - 手动输入参数（每行一个）
   - 点击"常用参数"添加基础配置
   - 点击"Java 9+ 模块参数"添加模块开放参数
   - 组合使用多个模板

### 2. 参数格式示例
```
# 系统属性配置
-Dfile.encoding=UTF-8
-Dprocess.profile=fstest
-Dspring.profiles.active=fstest
-Dlogback.configurationFile=/Users/<USER>/workspace/logback.xml

# Java 9+ 模块开放
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED
--add-opens=java.base/java.math=ALL-UNNAMED
--add-opens=java.base/java.lang=ALL-UNNAMED

# 性能调优
-server
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
```

### 3. 参数验证
- 系统会自动过滤空行和注释
- 每个非空行都会作为一个JVM参数传递给Tomcat
- 参数会在启动日志中显示，便于调试

## 📋 支持的参数类型

### 1. 系统属性 (-D)
- `-Dfile.encoding=UTF-8`
- `-Dspring.profiles.active=dev`
- `-Dlogback.configurationFile=/path/to/config.xml`

### 2. JVM选项
- `-server` / `-client`
- `-XX:+UseG1GC`
- `-XX:MaxGCPauseMillis=200`

### 3. 模块系统 (--add-opens)
- `--add-opens=java.base/java.lang.reflect=ALL-UNNAMED`
- `--add-opens=java.base/java.util=ALL-UNNAMED`

### 4. 内存设置
- 堆内存通过专门的"最小堆内存"和"最大堆内存"字段设置
- 其他内存参数可在JVM参数中配置：
  - `-XX:MetaspaceSize=256m`
  - `-XX:MaxMetaspaceSize=512m`

## 📋 修改的文件

1. **src/webview/HtmlTemplates.ts**
   - 添加JVM参数文本区域
   - 添加快捷按钮（常用参数、Java 9+参数、清空）
   - 添加参数处理JavaScript函数
   - 更新createInstance()函数解析JVM参数

2. **out/webview/HtmlTemplates.js**
   - 同步更新编译后的JavaScript文件

## 🎯 用户体验

### 修改前
- ❌ 无法配置JVM参数
- ❌ 只能设置堆内存大小
- ❌ 无法添加系统属性和模块参数

### 修改后
- ✅ **完整的JVM参数配置**：支持所有类型的JVM参数
- ✅ **快捷模板**：常用参数和Java 9+参数一键添加
- ✅ **注释支持**：便于参数组织和说明
- ✅ **智能解析**：自动处理空行和注释
- ✅ **实时生效**：参数立即应用到Tomcat启动

现在用户可以在创建Tomcat实例时完全自定义JVM参数，满足各种开发和生产环境的需求！
