# Source files
src/**
tsconfig.json
tslint.json

# Test files
test/**
.vscode-test/**

# Build files
.vscode/
.github/
node_modules/
*.vsix

# Documentation (keep only essential docs)
docs/**
*.md
!README.md

# Git files
.git/
.gitignore

# Development files
.eslintrc.json
.prettierrc
webpack.config.js

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Temporary folders
tmp/
temp/

# Demo files
demo-webapp/

# Local configuration
local.properties
config.local.json

# User data
.vscode/tomcat-instances/
tomcat-instances/
